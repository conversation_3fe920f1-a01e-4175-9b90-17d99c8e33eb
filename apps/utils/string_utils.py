import re


def to_snake_case(name: str) -> str:
    """
    Converts a PascalCase or camelCase string to snake_case.

    Examples:
        "CamelCase" -> "camel_case"
        "PascalCase" -> "pascal_case"
        "already_snake" -> "already_snake"
        "balanceOf" -> "balance_of"
    """
    if not isinstance(name, str) or not name:
        return ""

    # 1. 在大写字母前插入下划线 (但不在字符串开头)
    # e.g., "CamelCase" -> "_camel_Case"
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)

    # 2. 在小写/数字后跟大写字母前插入下划线
    # e.g., "_camel_Case" -> "_camel__case"
    s2 = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1)

    # 3. 转换为小写并移除多余的下划线
    return s2.lower()
