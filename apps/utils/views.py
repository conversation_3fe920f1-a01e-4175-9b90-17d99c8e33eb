import logging
from typing import Callable

from cobo_libs.api.restful.exceptions import (
    ApiInvalidParamException,
    ApiMissingParamException,
)
from cobo_libs.utils.exceptions.tools import check

logger = logging.getLogger("apps.utils")


def check_param(
    name: str, data: dict, check_func: Callable, constructor: Callable = None
):
    check(name in data, ApiMissingParamException, name)
    param_value = data[name]
    if check_func is not None:
        try:
            check(
                check_func(param_value),
                ApiInvalidParamException,
                f"{name}:{param_value}",
            )
        except Exception:
            logger.error(
                f"error checking param, name:{name}, data:{data}", exc_info=True
            )
            raise ApiInvalidParamException(f"{name}: {param_value}")
    if constructor is not None:
        try:
            return constructor(param_value)
        except Exception:
            logger.error(
                f"error construct param, name:{name}, data:{data}", exc_info=True
            )
            raise ApiInvalidParamException(f"{name}: {param_value}")
    else:
        return param_value


def check_optional_param(
    name: str, data: dict, check_func: Callable, constructor: Callable = None
):
    if name not in data:
        return None
    if data[name] is None:
        return None
    return check_param(name, data, check_func, constructor)
