import concurrent.futures


class ConcurrentExecutor:
    def __init__(self, max_workers=None):
        self.max_workers = max_workers

    def execute(self, tasks):
        """
        执行并发任务
        :param tasks: 任务列表，每个任务应为一个 (function, args) 元组
        :return: 包含结果和异常的字典
        """
        results = {}
        exceptions = {}
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=self.max_workers
        ) as executor:
            # 创建一个字典来存储任务的原始参数
            task_args = {}
            # 使用 id(future) 作为键
            future_to_task = {}

            for task in tasks:
                future = executor.submit(task[0], *task[1])
                future_to_task[future] = task
                task_args[id(future)] = task[1]

            for future in concurrent.futures.as_completed(future_to_task):
                task = future_to_task[future]
                args = task_args[id(future)]
                try:
                    result = future.result()
                    results[(task[0], tuple(args))] = result
                except Exception as exc:
                    exceptions[(task[0], tuple(args))] = exc
                    print(f"Task {task[0].__name__} generated an exception: {exc}")
        return results, exceptions
