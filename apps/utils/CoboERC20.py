COBO_ERC20_ABI = [
    {"type": "constructor", "inputs": [], "stateMutability": "nonpayable"},
    {
        "type": "function",
        "name": "BURNER_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "DEFAULT_ADMIN_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "MANAGER_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "MINTER_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "PAUSER_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "SALVAGER_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "UPGRADER_ROLE",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "UPGRADE_INTERFACE_VERSION",
        "inputs": [],
        "outputs": [{"name": "", "type": "string", "internalType": "string"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "accessListAdd",
        "inputs": [
            {"name": "accounts", "type": "address[]", "internalType": "address[]"}
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "accessListEnabled",
        "inputs": [],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "accessListRemove",
        "inputs": [
            {"name": "accounts", "type": "address[]", "internalType": "address[]"}
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "allowance",
        "inputs": [
            {"name": "owner", "type": "address", "internalType": "address"},
            {"name": "spender", "type": "address", "internalType": "address"},
        ],
        "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "approve",
        "inputs": [
            {"name": "spender", "type": "address", "internalType": "address"},
            {"name": "value", "type": "uint256", "internalType": "uint256"},
        ],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "balanceOf",
        "inputs": [{"name": "account", "type": "address", "internalType": "address"}],
        "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "blockListAdd",
        "inputs": [
            {"name": "accounts", "type": "address[]", "internalType": "address[]"}
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "blockListRemove",
        "inputs": [
            {"name": "accounts", "type": "address[]", "internalType": "address[]"}
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "burn",
        "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "burnFrom",
        "inputs": [
            {"name": "account", "type": "address", "internalType": "address"},
            {"name": "value", "type": "uint256", "internalType": "uint256"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "contractUri",
        "inputs": [],
        "outputs": [{"name": "", "type": "string", "internalType": "string"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "contractUriUpdate",
        "inputs": [{"name": "_uri", "type": "string", "internalType": "string"}],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "decimals",
        "inputs": [],
        "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "getAccessList",
        "inputs": [],
        "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "getBlockList",
        "inputs": [],
        "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "getRoleAdmin",
        "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "grantRole",
        "inputs": [
            {"name": "role", "type": "bytes32", "internalType": "bytes32"},
            {"name": "account", "type": "address", "internalType": "address"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "hasRole",
        "inputs": [
            {"name": "role", "type": "bytes32", "internalType": "bytes32"},
            {"name": "account", "type": "address", "internalType": "address"},
        ],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "initialize",
        "inputs": [
            {"name": "_name", "type": "string", "internalType": "string"},
            {"name": "_symbol", "type": "string", "internalType": "string"},
            {"name": "_uri", "type": "string", "internalType": "string"},
            {"name": "admin", "type": "address", "internalType": "address"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "isAccessListed",
        "inputs": [{"name": "account", "type": "address", "internalType": "address"}],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "isBlockListed",
        "inputs": [{"name": "account", "type": "address", "internalType": "address"}],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "mint",
        "inputs": [
            {"name": "to", "type": "address", "internalType": "address"},
            {"name": "amount", "type": "uint256", "internalType": "uint256"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "multicall",
        "inputs": [{"name": "data", "type": "bytes[]", "internalType": "bytes[]"}],
        "outputs": [{"name": "results", "type": "bytes[]", "internalType": "bytes[]"}],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "name",
        "inputs": [],
        "outputs": [{"name": "", "type": "string", "internalType": "string"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "pause",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "paused",
        "inputs": [],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "proxiableUUID",
        "inputs": [],
        "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "renounceRole",
        "inputs": [
            {"name": "role", "type": "bytes32", "internalType": "bytes32"},
            {"name": "account", "type": "address", "internalType": "address"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "revokeRole",
        "inputs": [
            {"name": "role", "type": "bytes32", "internalType": "bytes32"},
            {"name": "account", "type": "address", "internalType": "address"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "salvageERC20",
        "inputs": [
            {"name": "token", "type": "address", "internalType": "contract IERC20"},
            {"name": "amount", "type": "uint256", "internalType": "uint256"},
        ],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "salvageNative",
        "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "supportsInterface",
        "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "symbol",
        "inputs": [],
        "outputs": [{"name": "", "type": "string", "internalType": "string"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "toggleAccesslist",
        "inputs": [{"name": "enabled", "type": "bool", "internalType": "bool"}],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "totalSupply",
        "inputs": [],
        "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}],
        "stateMutability": "view",
    },
    {
        "type": "function",
        "name": "transfer",
        "inputs": [
            {"name": "to", "type": "address", "internalType": "address"},
            {"name": "amount", "type": "uint256", "internalType": "uint256"},
        ],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "transferFrom",
        "inputs": [
            {"name": "from", "type": "address", "internalType": "address"},
            {"name": "to", "type": "address", "internalType": "address"},
            {"name": "amount", "type": "uint256", "internalType": "uint256"},
        ],
        "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "unpause",
        "inputs": [],
        "outputs": [],
        "stateMutability": "nonpayable",
    },
    {
        "type": "function",
        "name": "upgradeToAndCall",
        "inputs": [
            {
                "name": "newImplementation",
                "type": "address",
                "internalType": "address",
            },
            {"name": "data", "type": "bytes", "internalType": "bytes"},
        ],
        "outputs": [],
        "stateMutability": "payable",
    },
    {
        "type": "function",
        "name": "version",
        "inputs": [],
        "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}],
        "stateMutability": "view",
    },
    {
        "type": "event",
        "name": "AccessListAddressAdded",
        "inputs": [
            {
                "name": "account",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "AccessListAddressRemoved",
        "inputs": [
            {
                "name": "account",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "AccesslistToggled",
        "inputs": [
            {"name": "", "type": "bool", "indexed": False, "internalType": "bool"}
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "Approval",
        "inputs": [
            {
                "name": "owner",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "spender",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "value",
                "type": "uint256",
                "indexed": False,
                "internalType": "uint256",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "BlockListAddressAdded",
        "inputs": [
            {
                "name": "account",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "BlockListAddressRemoved",
        "inputs": [
            {
                "name": "account",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "ContractUriUpdated",
        "inputs": [
            {
                "name": "caller",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "oldUri",
                "type": "string",
                "indexed": False,
                "internalType": "string",
            },
            {
                "name": "newUri",
                "type": "string",
                "indexed": False,
                "internalType": "string",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "Initialized",
        "inputs": [
            {
                "name": "version",
                "type": "uint64",
                "indexed": False,
                "internalType": "uint64",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "NativeSalvaged",
        "inputs": [
            {
                "name": "caller",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "amount",
                "type": "uint256",
                "indexed": True,
                "internalType": "uint256",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "Paused",
        "inputs": [
            {
                "name": "account",
                "type": "address",
                "indexed": False,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "RoleAdminChanged",
        "inputs": [
            {
                "name": "role",
                "type": "bytes32",
                "indexed": True,
                "internalType": "bytes32",
            },
            {
                "name": "previousAdminRole",
                "type": "bytes32",
                "indexed": True,
                "internalType": "bytes32",
            },
            {
                "name": "newAdminRole",
                "type": "bytes32",
                "indexed": True,
                "internalType": "bytes32",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "RoleGranted",
        "inputs": [
            {
                "name": "role",
                "type": "bytes32",
                "indexed": True,
                "internalType": "bytes32",
            },
            {
                "name": "account",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "sender",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "RoleRevoked",
        "inputs": [
            {
                "name": "role",
                "type": "bytes32",
                "indexed": True,
                "internalType": "bytes32",
            },
            {
                "name": "account",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "sender",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "TokenSalvaged",
        "inputs": [
            {
                "name": "caller",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "token",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "amount",
                "type": "uint256",
                "indexed": True,
                "internalType": "uint256",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "Transfer",
        "inputs": [
            {
                "name": "from",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "to",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            },
            {
                "name": "value",
                "type": "uint256",
                "indexed": False,
                "internalType": "uint256",
            },
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "Unpaused",
        "inputs": [
            {
                "name": "account",
                "type": "address",
                "indexed": False,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {
        "type": "event",
        "name": "Upgraded",
        "inputs": [
            {
                "name": "implementation",
                "type": "address",
                "indexed": True,
                "internalType": "address",
            }
        ],
        "anonymous": False,
    },
    {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []},
    {
        "type": "error",
        "name": "AccessControlUnauthorizedAccount",
        "inputs": [
            {"name": "account", "type": "address", "internalType": "address"},
            {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"},
        ],
    },
    {
        "type": "error",
        "name": "AddressEmptyCode",
        "inputs": [{"name": "target", "type": "address", "internalType": "address"}],
    },
    {
        "type": "error",
        "name": "BlockedAddress",
        "inputs": [{"name": "account", "type": "address", "internalType": "address"}],
    },
    {"type": "error", "name": "DefaultAdminError", "inputs": []},
    {
        "type": "error",
        "name": "ERC1967InvalidImplementation",
        "inputs": [
            {"name": "implementation", "type": "address", "internalType": "address"}
        ],
    },
    {"type": "error", "name": "ERC1967NonPayable", "inputs": []},
    {
        "type": "error",
        "name": "ERC20InsufficientAllowance",
        "inputs": [
            {"name": "spender", "type": "address", "internalType": "address"},
            {"name": "allowance", "type": "uint256", "internalType": "uint256"},
            {"name": "needed", "type": "uint256", "internalType": "uint256"},
        ],
    },
    {
        "type": "error",
        "name": "ERC20InsufficientBalance",
        "inputs": [
            {"name": "sender", "type": "address", "internalType": "address"},
            {"name": "balance", "type": "uint256", "internalType": "uint256"},
            {"name": "needed", "type": "uint256", "internalType": "uint256"},
        ],
    },
    {
        "type": "error",
        "name": "ERC20InvalidApprover",
        "inputs": [{"name": "approver", "type": "address", "internalType": "address"}],
    },
    {
        "type": "error",
        "name": "ERC20InvalidReceiver",
        "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}],
    },
    {
        "type": "error",
        "name": "ERC20InvalidSender",
        "inputs": [{"name": "sender", "type": "address", "internalType": "address"}],
    },
    {
        "type": "error",
        "name": "ERC20InvalidSpender",
        "inputs": [{"name": "spender", "type": "address", "internalType": "address"}],
    },
    {"type": "error", "name": "EnforcedPause", "inputs": []},
    {"type": "error", "name": "ExpectedPause", "inputs": []},
    {"type": "error", "name": "FailedCall", "inputs": []},
    {"type": "error", "name": "InvalidAddress", "inputs": []},
    {"type": "error", "name": "InvalidInitialization", "inputs": []},
    {
        "type": "error",
        "name": "NotAccessListAddress",
        "inputs": [{"name": "account", "type": "address", "internalType": "address"}],
    },
    {"type": "error", "name": "NotInitializing", "inputs": []},
    {
        "type": "error",
        "name": "SafeERC20FailedOperation",
        "inputs": [{"name": "token", "type": "address", "internalType": "address"}],
    },
    {"type": "error", "name": "SalvageNativeFailed", "inputs": []},
    {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []},
    {
        "type": "error",
        "name": "UUPSUnsupportedProxiableUUID",
        "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}],
    },
    {"type": "error", "name": "ZeroAmount", "inputs": []},
]
