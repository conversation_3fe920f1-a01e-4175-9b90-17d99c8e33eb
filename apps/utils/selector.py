"""
Tokenization Function Selector Utilities

This module provides utilities for working with Ethereum function selectors,
which are the first 4 bytes of the keccak256 hash of the function signature.
"""

from typing import Any, Dict, List, Optional, Tuple

from eth_utils import function_signature_to_4byte_selector
from web3 import Web3

from apps.utils.CoboERC20 import COBO_ERC20_ABI
from apps.utils.CoboFactory import COBO_FACTORY_ABI
from apps.utils.ProxyFactory import PROXY_FACTORY_ABI


class SelectorUtils:
    """Functions selector 工具类"""

    # 缓存计算好的 selector
    _selector_cache: Dict[str, str] = {}

    @classmethod
    def get_function_selector(cls, function_signature: str) -> str:
        """
        根据函数签名计算 selector

        Args:
            function_signature: 函数签名，如 "mint(address,uint256)"

        Returns:
            4字节的 selector，如 "40c10f19"
        """
        if function_signature in cls._selector_cache:
            return cls._selector_cache[function_signature]

        selector = function_signature_to_4byte_selector(function_signature).hex()
        cls._selector_cache[function_signature] = selector
        return selector

    @classmethod
    def get_cobo_erc20_selectors(cls) -> Dict[str, str]:
        """
        获取 CoboERC20 合约的所有函数 selector

        Returns:
            函数名到 selector 的映射
        """
        selectors = {}

        for abi_item in COBO_ERC20_ABI:
            if abi_item.get("type") == "function":
                name = abi_item["name"]
                inputs = abi_item.get("inputs", [])

                # 构建函数签名
                param_types = [input_item["type"] for input_item in inputs]
                function_signature = f"{name}({','.join(param_types)})"

                # 计算 selector
                selector = cls.get_function_selector(function_signature)
                selectors[name] = selector

        return selectors

    @classmethod
    def get_proxy_factory_selectors(cls) -> Dict[str, str]:
        """
        获取 ProxyFactory 合约的所有函数 selector

        Returns:
            函数名到 selector 的映射
        """
        selectors = {}

        for abi_item in PROXY_FACTORY_ABI:
            if abi_item.get("type") == "function":
                name = abi_item["name"]
                inputs = abi_item.get("inputs", [])

                # 构建函数签名
                param_types = [input_item["type"] for input_item in inputs]
                function_signature = f"{name}({','.join(param_types)})"

                # 计算 selector
                selector = cls.get_function_selector(function_signature)
                selectors[name] = selector

        return selectors

    @classmethod
    def get_cobo_factory_selectors(cls) -> Dict[str, str]:
        """
        获取 CoboFactory 合约的所有函数 selector

        Returns:
            函数名到 selector 的映射
        """
        selectors = {}

        for abi_item in COBO_FACTORY_ABI:
            if abi_item.get("type") == "function":
                name = abi_item["name"]
                inputs = abi_item.get("inputs", [])

                # 构建函数签名
                param_types = [input_item["type"] for input_item in inputs]
                function_signature = f"{name}({','.join(param_types)})"

                # 计算 selector
                selector = cls.get_function_selector(function_signature)
                selectors[name] = selector

        return selectors

    @classmethod
    def verify_selector_mapping(cls) -> Dict[str, str]:
        """
        验证并返回所有 CoboERC20 函数的 selector 映射
        用于调试和验证
        """
        return cls.get_cobo_erc20_selectors()


# 使用 web3 库动态构造 selector mapping
def _build_selectors_from_abi(abi: List[Dict]) -> Dict[str, str]:
    """
    从 ABI 构建函数名到 selector 的映射

    Args:
        abi: 合约 ABI

    Returns:
        函数名到 selector 的映射
    """
    selectors = {}

    for item in abi:
        if item.get("type") == "function":
            name = item["name"]
            inputs = item.get("inputs", [])

            # 构建函数签名
            param_types = [input_item["type"] for input_item in inputs]
            function_signature = f"{name}({','.join(param_types)})"

            # 计算 selector
            selector = Web3.keccak(text=function_signature)[:4].hex()
            selectors[name] = selector

    return selectors


# 动态构造 CoboERC20 函数 selector 映射
COBO_ERC20_SELECTORS = _build_selectors_from_abi(COBO_ERC20_ABI)

# 动态构造 ProxyFactory 函数 selector 映射
PROXY_FACTORY_SELECTORS = _build_selectors_from_abi(PROXY_FACTORY_ABI)

# 动态构造 CoboFactory 函数 selector 映射
COBO_FACTORY_SELECTORS = _build_selectors_from_abi(COBO_FACTORY_ABI)


def get_selector_by_name(function_name: str) -> str:
    """根据函数名获取 selector"""
    # 先在 CoboERC20 中查找
    if function_name in COBO_ERC20_SELECTORS:
        return COBO_ERC20_SELECTORS[function_name]

    # 再在 ProxyFactory 中查找
    if function_name in PROXY_FACTORY_SELECTORS:
        return PROXY_FACTORY_SELECTORS[function_name]

    # 最后在 CoboFactory 中查找
    if function_name in COBO_FACTORY_SELECTORS:
        return COBO_FACTORY_SELECTORS[function_name]

    return ""


def get_name_by_selector(selector: str) -> str:
    """根据 selector 获取函数名"""
    selector = selector.lower()

    # 先在 CoboERC20 中查找
    for name, sel in COBO_ERC20_SELECTORS.items():
        if sel.lower() == selector:
            return name

    # 再在 ProxyFactory 中查找
    for name, sel in PROXY_FACTORY_SELECTORS.items():
        if sel.lower() == selector:
            return name

    # 最后在 CoboFactory 中查找
    for name, sel in COBO_FACTORY_SELECTORS.items():
        if sel.lower() == selector:
            return name

    return ""


def get_contract_type_by_selector(selector: str) -> str:
    """根据 selector 判断合约类型"""
    selector = selector.lower()
    if not selector.startswith("0x"):
        selector = "0x" + selector
    # 检查是否为 CoboERC20 的函数
    for sel in COBO_ERC20_SELECTORS.values():
        if sel.lower() == selector:
            return "CoboERC20"

    # 检查是否为 ProxyFactory 的函数
    for sel in PROXY_FACTORY_SELECTORS.values():
        if sel.lower() == selector:
            return "ProxyFactory"

    # 检查是否为 CoboFactory 的函数
    for sel in COBO_FACTORY_SELECTORS.values():
        if sel.lower() == selector:
            return "CoboFactory"

    return "Unknown"


def get_abi_by_selector(selector: str) -> List[Dict]:
    """根据 selector 获取对应的 ABI"""
    contract_type = get_contract_type_by_selector(selector)

    if contract_type == "CoboERC20":
        return COBO_ERC20_ABI
    elif contract_type == "ProxyFactory":
        return PROXY_FACTORY_ABI
    elif contract_type == "CoboFactory":
        return COBO_FACTORY_ABI
    else:
        # 默认返回 CoboERC20_ABI，保持向后兼容
        return COBO_ERC20_ABI


def is_valid_selector(selector: str) -> bool:
    """检查是否为有效的函数 selector"""
    return get_contract_type_by_selector(selector) != "Unknown"


def smart_decode_function_input(calldata: str) -> Optional[Tuple[Any, Dict[str, Any]]]:
    """
    智能解析 calldata，根据 selector 自动选择正确的 ABI

    Args:
        calldata: 交易的 calldata (十六进制字符串)

    Returns:
        解码后的函数对象和参数字典，如果解码失败则返回 None
    """
    if not calldata or len(calldata) < 10:  # 0x + 8位选择器
        return None

    # 提取 selector
    selector = calldata[2:10]

    # 根据 selector 获取对应的 ABI
    abi = get_abi_by_selector(selector)

    # 使用对应的 ABI 解析 calldata
    from apps.utils.web3 import decode_function_input

    return decode_function_input(abi, calldata)
