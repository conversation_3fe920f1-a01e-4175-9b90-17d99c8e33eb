# singleton.py
from .concurrent_executor import ConcurrentExecutor


class SingletonMeta(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]


class ConcurrentExecutorSingleton(metaclass=SingletonMeta):
    def __init__(self, max_workers=None):
        self.executor = ConcurrentExecutor(max_workers)

    def execute(self, tasks):
        return self.executor.execute(tasks)


# 创建单例的并发执行器
concurrent_executor = ConcurrentExecutorSingleton(max_workers=6)


def get_address_token_balances(wallet_id: str, address: str, token_ids: list[str]):
    return "success"


# tasks = [
#     (self.get_address_token_balances, [wallet_id, address.address, token_ids])
#     for address in addresses
# ]

# 并发器执行任务
# results, exceptions = concurrent_executor.execute(tasks)
# for task, result in results.items():
#     address_balance_dict[task[1][1]] = result
# logger.debug(f"address_balance_dict: {address_balance_dict}")
# return address_balance_dict
