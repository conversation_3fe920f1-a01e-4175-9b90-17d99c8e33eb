import logging
from functools import wraps

from app_libs.auth.managers import UserSessionManager
from django.core.exceptions import PermissionDenied

from apps.enums import TokenizationFunction
from apps.utils.permissions_settings import PermissionsSettings

logger = logging.getLogger("apps.permission")


def permission_checker(func_name: TokenizationFunction):
    """
    检查权限
    :param func_name: 鉴权function的名称
    """

    def wrapper(fn):
        @wraps(fn)
        def inner(*args, **kwargs):
            request = None
            for arg in args:
                if hasattr(arg, "user"):
                    logger.info(f"get user arg:{arg}")
                    request = arg

            if request is None:
                raise PermissionDenied

            user = request.user

            if not hasattr(user, "role_names"):
                logger.warning(f"can't check user roles request:{request}")
                raise PermissionDenied

            roles = PermissionsSettings.get_permission_list(func_name)

            logger.info(
                f"check user roles: {user.role_names} {func_name} app_roles: {roles}"
            )

            has_permission = UserSessionManager.check_user_role(user=user, roles=roles)
            if not has_permission:
                raise PermissionDenied

            return fn(*args, **kwargs)

        return inner

    return wrapper
