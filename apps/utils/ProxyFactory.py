PROXY_FACTORY_ABI = [
    {"inputs": [], "name": "InvalidAddress", "type": "error"},
    {
        "inputs": [
            {"internalType": "uint256", "name": "salt", "type": "uint256"},
            {"internalType": "address", "name": "coboERC20Logic", "type": "address"},
            {"internalType": "string", "name": "name", "type": "string"},
            {"internalType": "string", "name": "symbol", "type": "string"},
            {"internalType": "string", "name": "uri", "type": "string"},
            {"internalType": "uint8", "name": "decimal", "type": "uint8"},
            {"internalType": "address[]", "name": "admins", "type": "address[]"},
            {"internalType": "address[]", "name": "managers", "type": "address[]"},
            {"internalType": "address[]", "name": "minters", "type": "address[]"},
            {"internalType": "address[]", "name": "burners", "type": "address[]"},
            {"internalType": "address[]", "name": "pausers", "type": "address[]"},
            {"internalType": "address[]", "name": "salvagers", "type": "address[]"},
            {"internalType": "address[]", "name": "upgraders", "type": "address[]"},
        ],
        "name": "deployAndInit",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
