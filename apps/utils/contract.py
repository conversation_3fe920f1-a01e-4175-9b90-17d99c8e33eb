import logging

from web3 import Web3

from apps.utils.CoboERC20 import COBO_ERC20_ABI
from apps.utils.ProxyFactory import PROXY_FACTORY_ABI
from apps.utils.web3 import web3_contract

logger = logging.getLogger("waas2.tokenization")


class CoboERC20ContractHelper:
    """
    Token 合约辅助类
    """

    _FACTORY_ADDRESSES = {"SETH": "******************************************"}

    _TEMPLATE_ADDRESSES = {"SETH": "******************************************"}

    @classmethod
    def get_contract(cls, chain_id: str, token_address: str):
        """获取代币合约实例"""
        return web3_contract(
            chain_id,
            Web3.to_checksum_address(token_address),
            COBO_ERC20_ABI,
        )

    @classmethod
    def get_factory_address(cls, chain_id: str):
        """获取代币合约实例"""
        factory_address = cls._FACTORY_ADDRESSES.get(chain_id)
        if not factory_address:
            raise ValueError(f"Unsupported chain {chain_id}")
        return factory_address

    @classmethod
    def get_template_address(cls, chain_id: str):
        """获取代币合约实例"""
        factory_address = cls._TEMPLATE_ADDRESSES.get(chain_id)
        if not factory_address:
            raise ValueError(f"Unsupported chain {chain_id}")
        return factory_address

    @classmethod
    def get_factory_contract(cls, chain_id: str):
        """获取代币合约实例"""
        return web3_contract(
            chain_id,
            cls.get_factory_address(chain_id),
            PROXY_FACTORY_ABI,
        )
