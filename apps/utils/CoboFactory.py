COBO_FACTORY_ABI = [
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": True,
                "internalType": "address",
                "name": "_contract",
                "type": "address",
            },
            {
                "indexed": True,
                "internalType": "address",
                "name": "_deployer",
                "type": "address",
            },
        ],
        "name": "ContractDeployed",
        "type": "event",
    },
    {
        "inputs": [
            {
                "internalType": "enum CoboFactory.DeployType",
                "name": "typ",
                "type": "uint8",
            },
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "bytes", "name": "initCode", "type": "bytes"},
        ],
        "name": "deploy",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum CoboFactory.DeployType",
                "name": "typ",
                "type": "uint8",
            },
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "bytes", "name": "initCode", "type": "bytes"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
        ],
        "name": "deployAndInit",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "bytes", "name": "initCode", "type": "bytes"},
            {"internalType": "bool", "name": "_private", "type": "bool"},
            {"internalType": "bool", "name": "_emit", "type": "bool"},
        ],
        "name": "deployCreate2",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "bytes", "name": "initCode", "type": "bytes"},
            {"internalType": "bool", "name": "_private", "type": "bool"},
            {"internalType": "bool", "name": "_emit", "type": "bool"},
        ],
        "name": "deployCreate3",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {
                "internalType": "enum CoboFactory.DeployType",
                "name": "typ",
                "type": "uint8",
            },
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "bytes", "name": "initCode", "type": "bytes"},
        ],
        "name": "getAddress",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "bytes", "name": "initCode", "type": "bytes"},
        ],
        "name": "getCreate2Address",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes32", "name": "salt", "type": "bytes32"},
            {"internalType": "address", "name": "sender", "type": "address"},
        ],
        "name": "getCreate3Address",
        "outputs": [
            {"internalType": "address", "name": "_contract", "type": "address"}
        ],
        "stateMutability": "view",
        "type": "function",
    },
]
