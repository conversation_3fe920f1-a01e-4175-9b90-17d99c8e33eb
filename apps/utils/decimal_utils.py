from decimal import Decimal


class DecimalUtils:
    QUANTIZE_MODE = Decimal("1.000000")

    @classmethod
    def int_to_float(
        cls, amount: Decimal, decimal: int, quantize: str = None, rounding: str = None
    ) -> Decimal:
        float_amount = Decimal(amount) / 10**decimal

        if quantize is not None:
            if rounding is not None:
                float_amount = float_amount.quantize(
                    Decimal(quantize), rounding=rounding
                )
            else:
                float_amount = float_amount.quantize(Decimal(quantize))

        return float_amount

    @classmethod
    def float_to_int(
        cls,
        amount: float | str | Decimal,
        decimal: int,
        quantize: str = None,
        rounding: str = None,
    ) -> Decimal:
        float_amount = Decimal(amount)
        if quantize is not None:
            if rounding is not None:
                float_amount = float_amount.quantize(
                    Decimal(quantize), rounding=rounding
                )
            else:
                float_amount = float_amount.quantize(Decimal(quantize))

        int_amount = float_amount * 10**decimal
        return int_amount
