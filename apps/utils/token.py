import json
import logging
import time
from decimal import Decimal
from typing import Dict, Optional

from cobo_libs.utils.caches import cache_instance
from cobo_libs.utils.json_helper.djson import DecimalEncoder
from django.conf import settings

from apps.clients.waas_internal_client import WaaSInternalApiClientManager
from apps.tokenization.data.token import CacheTokenInfo, TokenInfo
from apps.waas.client.generated.waas2_client.api.tokens_api import TokensApi

logger = logging.getLogger("bridge")


class TokenCacheManager(object):
    _CACHE_EXPIRY = 86400  # 1 day
    _local_cache: Dict[str, TokenInfo] = {}
    _local_cache_timestamp: int = 0

    @classmethod
    def reload_token_infos(cls) -> dict:
        logger.info(
            f"Fetching token list from WaaS API, org_id: {settings.COINS_ORG_ID}"
        )
        with WaaSInternalApiClientManager.get_web_client(
            settings.COINS_ORG_ID
        ) as client:
            api = TokensApi(client)
            logger.info("Calling supported_tokens API")
            response = api.supported_tokens(ignore_token_rate=0)
            logger.info(f"API call successful, received {len(response.result)} tokens")

        tokens = []
        for x in response.result:
            usd_rate = None
            if x.usd_rate is not None:
                usd_rate = Decimal(x.usd_rate)
            token_info = CacheTokenInfo(
                token_id=x.token_id,
                chain_id=x.chain_id,
                asset_id=x.asset_id or "",
                decimal=x.decimal,
                fee_token_id=x.fee_token_id,
                dust_threshold=int(x.dust_threshold) if x.dust_threshold else 0,
                minimum_deposit_threshold=(
                    int(x.minimum_deposit_threshold)
                    if x.minimum_deposit_threshold
                    else 0
                ),
                require_memo=x.require_memo,
                symbol=x.symbol or "",
                name=x.name or "",
                icon_url=x.icon_url or "",
                token_address=x.token_address or "",
                usd_rate=usd_rate or Decimal(0),
            )
            tokens.append(token_info)

        cache_data = {
            "tokens": [x.model_dump() for x in tokens],
            "timestamp": int(time.time()),
        }

        cache_key = cls._get_cache_key()
        logger.info(
            f"Updating Redis cache, key: {cache_key}, token count: {len(tokens)}"
        )
        cache_instance.set(cache_key, json.dumps(cache_data, cls=DecimalEncoder))

        cls._local_cache = {x.token_id: x for x in tokens}
        cls._local_cache_timestamp = cache_data.get("timestamp")

        logger.info("Token cache loading completed successfully")
        return cache_data

    @classmethod
    def get_token_info(cls, token_id: str) -> Optional[CacheTokenInfo]:
        try:
            now_ts = int(time.time())
            reload_needed = False

            # Check if reload is needed
            if not cls._local_cache:
                logger.info(
                    f"Cache is empty, need to load token info, requested token: {token_id}"
                )
                reload_needed = True
            elif now_ts - cls._local_cache_timestamp > cls._CACHE_EXPIRY:
                cache_age = now_ts - cls._local_cache_timestamp
                logger.info(
                    f"Cache expired, age: {cache_age}s, expiry: {cls._CACHE_EXPIRY}s, requested token: {token_id}"
                )
                reload_needed = True

            if reload_needed:
                logger.info(f"Reloading token cache for token: {token_id}")
                cls.reload_token_infos()

            token_info = cls._local_cache.get(token_id)
            if token_info:
                return token_info
            else:
                cache_size = len(cls._local_cache) if cls._local_cache else 0
                logger.warning(
                    f"Token not found in cache: {token_id}, cache contains {cache_size} tokens"
                )
                return None

        except Exception as e:
            logger.warning(
                f"Error getting token info: {str(e)}, token_id: {token_id}",
                exc_info=True,
            )
            # Best effort to return a result
            if cls._local_cache and token_id in cls._local_cache:
                logger.info(
                    f"Despite exception, returning token from cache: {token_id}"
                )
                return cls._local_cache.get(token_id)
            return None

    @classmethod
    def _get_cache_key(cls):
        return "apps.swap.token.cache"


class TokenManager(object):
    @classmethod
    def get_token_info(cls, token_id: str) -> TokenInfo | None:
        cache_token_info = TokenCacheManager.get_token_info(token_id)
        if not cache_token_info:
            return None
        if cache_token_info.token_id == cache_token_info.chain_id:
            chain = cache_token_info
        else:
            chain = TokenCacheManager.get_token_info(cache_token_info.chain_id)
            if not chain:
                return None
        result = TokenInfo(**cache_token_info.__dict__, chain=chain)
        return result
