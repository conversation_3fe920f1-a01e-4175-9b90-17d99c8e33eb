from typing import List

from apps.enums import PortalUserRole, TokenizationFunction

PermissionMap = {
    "mint": [PortalUserRole.Operator, PortalUserRole.Manager, PortalUserRole.Admin],
    "burn": [PortalUserRole.Operator, PortalUserRole.Manager, PortalUserRole.Admin],
    "enable": [PortalUserRole.Operator, PortalUserRole.Manager, PortalUserRole.Admin],
    "disable": [PortalUserRole.Operator, PortalUserRole.Manager, PortalUserRole.Admin],
    "restricted": [
        PortalUserRole.Operator,
        PortalUserRole.Manager,
        PortalUserRole.Admin,
    ],
    "access": [PortalUserRole.Operator, PortalUserRole.Manager, PortalUserRole.Admin],
    "allowlist": [
        PortalUserRole.Operator,
        PortalUserRole.Manager,
        PortalUserRole.Admin,
    ],
    "blocklist": [
        PortalUserRole.Operator,
        PortalUserRole.Manager,
        PortalUserRole.Admin,
    ],
    "permissions": [PortalUserRole.Manager, PortalUserRole.Admin],
    "paused": [PortalUserRole.Manager, PortalUserRole.Admin],
    "unpaused": [PortalUserRole.Manager, PortalUserRole.Admin],
    "more_action_write": [PortalUserRole.Manager, PortalUserRole.Admin],
}


class PermissionsSettings:
    @classmethod
    def get_permission_list(
        cls, func_name: TokenizationFunction
    ) -> List[PortalUserRole]:
        return PermissionMap.get(func_name, [])
