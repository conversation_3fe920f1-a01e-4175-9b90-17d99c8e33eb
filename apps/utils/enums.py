from enum import Enum
from typing import List, Optional


class StrEnum(str, Enum):
    @classmethod
    def types(cls) -> List["StrEnum"]:
        return list(cls.__members__.values())

    @classmethod
    def is_valid(cls, type_str: str) -> bool:
        return cls.from_value(type_str) is not None

    @classmethod
    def from_value(cls, value: str) -> Optional["StrEnum"]:
        return {x.value.lower(): x for x in cls}.get(value.lower())
