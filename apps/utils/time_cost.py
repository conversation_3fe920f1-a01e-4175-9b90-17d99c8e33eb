import logging
import time
from functools import wraps

logger = logging.getLogger("tokenization")


def time_it(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()  # 记录开始时间（秒）
        result = func(*args, **kwargs)
        end_time = time.time()  # 记录结束时间（秒）
        duration_ms = (end_time - start_time) * 1000  # 计算耗时（毫秒）
        logger.info(
            f"Function {func.__name__} took {duration_ms:.2f} milliseconds to complete."
        )
        print(
            f"Function {func.__name__} took {duration_ms:.2f} milliseconds to complete."
        )
        return result

    return wrapper
