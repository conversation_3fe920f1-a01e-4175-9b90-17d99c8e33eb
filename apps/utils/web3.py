from typing import Any, Dict, List, Optional, <PERSON><PERSON>

import eth_abi
from django.conf import settings
from eth_utils import function_abi_to_4byte_selector
from web3 import Web3
from web3.contract import Contract
from web3.middleware import geth_poa_middleware

NATIVE_TOKEN_ADDRESS = "******************************************"

NULL_ADDRESS = "******************************************"

ROLE_MAPPING = {
    "MINTER_ROLE": "0x9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a6",
    "BURNER_ROLE": "0x3c11d16cbaffd01df69ce1c404f6340ee057498f5f00246190ea54220576a848",
    "PAUSER_ROLE": "0x65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a",
    "MANAGER_ROLE": "0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08",
    "SALVAGER_ROLE": "0x9e16e6c596d2cf298b0b08786bf3a653eb1a9f723a5a76b814e3fbaa4f944609",
    "UPGRADER_ROLE": "0x189ab7a9244df0848122154315af71fe140f3db0fe014031783b0946b8c9d2e3",
    "DEFAULT_ADMIN_ROLE": "******************************************000000000000000000000000",
}


def encode_calldata(func_name, *args):
    # 分解函数名称和参数类型
    func_name, types = func_name.split("(")
    types = types.rstrip(")").split(",")
    types = [t.strip() for t in types if t]

    # 生成函数选择器
    func_selector = function_abi_to_4byte_selector(
        {"name": func_name, "inputs": [{"type": t} for t in types], "type": "function"}
    )

    # 编码参数
    encoded_args = Web3().codec.encode(types, args)
    data = func_selector + encoded_args
    return f"0x{data.hex()}"


def str_to_bytes32(_str: str) -> bytes:
    return eth_abi.abi.encode(["bytes32"], [bytes(_str, "utf-8")])


def decode_call_result(types, hex_data: str):
    return Web3().codec.decode(types, bytes.fromhex(hex_data.replace("0x", "")))


def web3_provider(chain_id: str) -> str:
    return settings.WEB3_PROVIDER_MAP.get(chain_id)


def web3_instance(chain_id: str, provider: str = None) -> Web3:
    if not provider:
        provider = web3_provider(chain_id)
    web3 = Web3(Web3.HTTPProvider(provider))
    web3.middleware_onion.inject(geth_poa_middleware, layer=0)
    web3.strict_bytes_type_checking = False
    return web3


def web3_contract(
    chain_id: str, contract_address: str, abi: List[Dict], provider: str = None
) -> Contract:
    return web3_instance(chain_id, provider).eth.contract(
        address=Web3.to_checksum_address(contract_address), abi=abi
    )


def get_tx_gas_fee(chain_id: str, tx_hash: str):
    web3 = web3_instance(chain_id)
    receipt = web3.eth.get_transaction_receipt(tx_hash)
    gas = receipt["gasUsed"] * receipt["effectiveGasPrice"]
    if chain_id in ["BASE_ETH", "OPT_ETH"]:
        gas += int(receipt["l1Fee"], base=16)
    return gas


def _make_params_serializable(data: Any) -> Any:
    """
    递归地遍历一个数据结构，并将非JSON序列化的类型（如bytes）
    转换为可序列化的格式（如十六进制字符串）。
    """
    if isinstance(data, bytes):
        return "0x" + data.hex()
    if isinstance(data, (list, tuple)):
        # 重构相同类型的 list 或 tuple
        return type(data)(_make_params_serializable(item) for item in data)
    # web3.py 返回的参数字典是 AttributeDict 类型，它也带有 .items() 方法
    if hasattr(data, "items"):
        return {key: _make_params_serializable(value) for key, value in data.items()}

    # 如果值已经是可序列化的（int, str, bool等），则原样返回
    return data


def decode_function_input(
    contract_abi: List[Dict[str, Any]],
    calldata: Any,
) -> Optional[Tuple[Any, Dict[str, Any]]]:
    """
    使用合约 ABI 解码交易的 calldata。
    如果 calldata 是一个 multicall 调用，它会递归解码内部的子调用。
    返回的参数字典保证是JSON可序列化的。

    Args:
        contract_abi: 合约的 ABI 列表。
        calldata: 交易的 calldata (可以是十六进制字符串或bytes)。

    Returns:
        一个元组，包含解码后的函数对象和已完全序列化的参数字典。
        如果解码失败，则返回 None。
    """
    if isinstance(calldata, bytes):
        calldata = "0x" + calldata.hex()

    if not calldata or not isinstance(calldata, str) or not calldata.startswith("0x"):
        return None

    w3 = Web3()
    contract = w3.eth.contract(abi=contract_abi)

    try:
        func_obj, func_params = contract.decode_function_input(calldata)
    except ValueError:
        return None

    # 如果是 multicall，递归解码其内部数据
    # 使用selector判断而不是函数名，更加准确
    calldata_selector = calldata[2:10].lower() if len(calldata) >= 10 else None
    if calldata_selector == "ac9650d8" and "data" in func_params:
        decoded_inner_calls = []
        for inner_calldata_bytes in func_params["data"]:
            # 从内部calldata中提取selector
            inner_calldata_hex = "0x" + inner_calldata_bytes.hex()
            inner_selector = (
                inner_calldata_hex[2:10] if len(inner_calldata_hex) >= 10 else None
            )

            # 递归调用将返回已序列化的参数
            inner_decoded_data = decode_function_input(
                contract_abi, inner_calldata_bytes
            )

            if inner_decoded_data:
                inner_func_obj, inner_func_params = inner_decoded_data
                decoded_inner_calls.append(
                    {
                        "function": inner_func_obj.fn_name,
                        "params": inner_func_params,
                        "selector": inner_selector,
                    }
                )
            else:
                # 如果内部调用解码失败，记录错误
                decoded_inner_calls.append(
                    {
                        "error": "Failed to decode inner call",
                        "calldata": inner_calldata_hex,
                        "selector": inner_selector,
                    }
                )
        # 用解码后的列表替换原始的 bytes 列表
        func_params["data"] = decoded_inner_calls

    # 在返回之前，对整个参数字典进行序列化处理
    serializable_params = _make_params_serializable(func_params)

    return func_obj, serializable_params


def encode_function_input(
    contract_abi: List[Dict[str, Any]],
    fn_name: str,
    fn_params: Dict[str, Any],
) -> str:
    """
    根据函数名和参数字典，将其编码为十六进制的 calldata。
    可以处理 multicall，递归地编码其内部调用。
    新增：自动转换特定类型的参数，例如将 'BURNER_ROLE' 字符串转换为 bytes32。

    Args:
        contract_abi: 合约的 ABI 列表。
        fn_name: 要调用的函数名。
        fn_params: 一个包含参数名和值的字典。

    Returns:
        编码后的十六进制 calldata 字符串。

    Raises:
        ValueError: 如果函数名在ABI中不存在，或者参数不匹配。
    """
    w3 = Web3()
    contract = w3.eth.contract(abi=contract_abi)

    # 从 ABI 中找到对应的函数定义
    func_abi = next(
        (
            item
            for item in contract.abi
            if item.get("type") == "function" and item.get("name") == fn_name
        ),
        None,
    )
    if not func_abi:
        raise ValueError(f"Function '{fn_name}' not found in ABI.")

    # 如果是 multicall，它的参数（一个bytes数组）需要特殊处理
    if fn_name == "multicall" and "data" in fn_params:
        encoded_inner_calls = []
        for inner_call in fn_params.get("data", []):
            inner_fn_name = inner_call.get("function")
            inner_fn_params = inner_call.get("params")
            if not inner_fn_name or inner_fn_params is None:
                raise ValueError(
                    f"Invalid inner call format for multicall: {inner_call}"
                )
            encoded_call_hex = encode_function_input(
                contract_abi, inner_fn_name, inner_fn_params
            )
            encoded_inner_calls.append(w3.to_bytes(hexstr=encoded_call_hex))

        args_list = [encoded_inner_calls]
    else:
        # 对于普通函数，按ABI定义的顺序准备和转换参数
        processed_args = []
        try:
            for param_abi in func_abi["inputs"]:
                param_name = param_abi["name"]
                param_type = param_abi["type"]
                raw_value = fn_params[param_name]

                # --- 智能类型转换 ---
                converted_value = raw_value

                # 处理地址类型参数
                if param_type == "address" and isinstance(raw_value, str):
                    # 确保地址是checksum格式
                    try:
                        converted_value = Web3.to_checksum_address(raw_value)
                    except ValueError as e:
                        raise ValueError(
                            f"Invalid address format for parameter '{param_name}': {e}"
                        )

                # 处理地址数组类型参数
                elif param_type == "address[]" and isinstance(raw_value, list):
                    # 将列表中的每个地址转换为checksum格式
                    try:
                        converted_value = [
                            Web3.to_checksum_address(addr) for addr in raw_value
                        ]
                    except ValueError as e:
                        raise ValueError(
                            f"Invalid address in array for parameter '{param_name}': {e}"
                        )

                # 处理bytes类型参数
                elif (
                    param_type.startswith("bytes")
                    and isinstance(raw_value, str)
                    and not raw_value.startswith("0x")
                ):
                    # 首先检查是否是角色字符串，如果是则直接使用映射值
                    if param_type == "bytes32" and raw_value in ROLE_MAPPING:
                        # 将角色字符串转换为对应的bytes32哈希值
                        role_hex = ROLE_MAPPING[raw_value]
                        converted_value = w3.to_bytes(hexstr=role_hex)
                    else:
                        # 假设它是一个需要哈希的字符串
                        converted_value = w3.keccak(text=raw_value)

                        # 如果是固定长度的bytes，例如bytes4，则进行截断
                        # 正则表达式匹配 bytes<M> 中的数字M
                        import re

                        match = re.match(r"bytes(\d+)", param_type)
                        if match:
                            num_bytes = int(match.group(1))
                            # 从哈希结果中截取正确长度的字节
                            converted_value = converted_value[:num_bytes]

                # 处理数值类型参数
                elif (
                    param_type.startswith("uint") or param_type.startswith("int")
                ) and isinstance(raw_value, str):
                    # Convert numeric strings to integers
                    converted_value = int(raw_value)

                processed_args.append(converted_value)
        except KeyError as e:
            raise ValueError(
                f"Missing parameter '{e.args[0]}' for function '{fn_name}'."
            )

        args_list = processed_args

    # 使用 web3.py 进行编码
    encoded_data = contract.encodeABI(fn_name=fn_name, args=args_list)
    return encoded_data
