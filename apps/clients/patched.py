import hashlib
import json
import logging
import re
import time
from urllib.parse import urlparse

import urllib3
from cobo_libs.utils.codec import force_text
from cobo_libs.utils.logger import timing_logger
from django.conf import settings
from nacl.signing import SigningKey

from apps.waas.client.generated.waas2_client import (
    ApiClient,
    ApiException,
    ApiValueError,
)
from apps.waas.client.generated.waas2_client.rest import RESTClientObject, RESTResponse

logger = logging.getLogger("apps.auths")


class _PatchedRestClient(RESTClientObject):
    _api_secret: str

    def __init__(self, api_secret, configuration):
        self._api_secret = api_secret
        super(_PatchedRestClient, self).__init__(configuration=configuration)

    def _generate_request_signature(
        self, method, url, headers=None, body=None, post_params=None
    ):
        api_nonce = str(int(time.time() * 1e6))
        url_path = urlparse(url).path
        url_query = urlparse(url).query
        signing_content = "|".join(
            (method, url_path, api_nonce, url_query, force_text(body or ""))
        )
        digest = hashlib.sha256(
            hashlib.sha256(signing_content.encode()).digest()
        ).digest()
        sk = SigningKey(bytes.fromhex(self._api_secret))
        signature = sk.sign(digest).signature
        vk = bytes(sk.verify_key)
        headers = headers or {}
        headers.update(
            {
                "biz-api-key": vk.hex(),
                "biz-api-nonce": api_nonce,
                "biz-api-signature": signature.hex(),
            }
        )
        logger.info(
            f"request, {method} {url}, "
            f"header:{headers}, body:{body}, post_params:{post_params}",
        )
        return headers

    def request(
        self,
        method,
        url,
        headers=None,
        body=None,
        post_params=None,
        _request_timeout=None,
    ):
        """Perform requests.

        :param method: http request method
        :param url: http request url
        :param headers: http request headers
        :param body: request json body, for `application/json`
        :param post_params: request post parameters,
                            `application/x-www-form-urlencoded`
                            and `multipart/form-data`
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        """
        if not _request_timeout:
            _request_timeout = settings.WAAS_REQUEST_TIMEOUT

        with timing_logger(f"WAAS2|{method}|{url}"):
            method = method.upper()
            assert method in [
                "GET",
                "HEAD",
                "DELETE",
                "POST",
                "PUT",
                "PATCH",
                "OPTIONS",
            ]

            if post_params and body:
                raise ApiValueError(
                    "body parameter cannot be used with post_params parameter."
                )

            post_params = post_params or {}
            headers = headers or {}

            timeout = None
            if _request_timeout:
                if isinstance(_request_timeout, (int, float)):
                    timeout = urllib3.Timeout(total=_request_timeout)
                elif isinstance(_request_timeout, tuple) and len(_request_timeout) == 2:
                    timeout = urllib3.Timeout(
                        connect=_request_timeout[0], read=_request_timeout[1]
                    )

            try:
                # For `POST`, `PUT`, `PATCH`, `OPTIONS`, `DELETE`
                if method in ["GET", "POST", "PUT", "PATCH", "OPTIONS", "DELETE"]:
                    # no content type provided or payload is json
                    content_type = headers.get("Content-Type")
                    if not content_type or re.search(
                        "json", content_type, re.IGNORECASE
                    ):
                        request_body = None
                        if body is not None:
                            request_body = json.dumps(body)

                        headers = self._generate_request_signature(
                            method=method, url=url, headers=headers, body=request_body
                        )
                        r = self.pool_manager.request(
                            method,
                            url,
                            body=request_body,
                            timeout=timeout,
                            headers=headers,
                            preload_content=False,
                        )
                    elif content_type == "application/x-www-form-urlencoded":
                        headers = self._generate_request_signature(
                            method=method,
                            url=url,
                            headers=headers,
                            post_params=post_params,
                        )
                        r = self.pool_manager.request(
                            method,
                            url,
                            fields=post_params,
                            encode_multipart=False,
                            timeout=timeout,
                            headers=headers,
                            preload_content=False,
                        )
                    elif content_type == "multipart/form-data":
                        # must del headers['Content-Type'], or the correct
                        # Content-Type which generated by urllib3 will be
                        # overwritten.
                        del headers["Content-Type"]
                        # Ensures that dict objects are serialized
                        post_params = [
                            (a, json.dumps(b)) if isinstance(b, dict) else (a, b)
                            for a, b in post_params
                        ]
                        headers = self._generate_request_signature(
                            method=method,
                            url=url,
                            headers=headers,
                            post_params=post_params,
                        )
                        r = self.pool_manager.request(
                            method,
                            url,
                            fields=post_params,
                            encode_multipart=True,
                            timeout=timeout,
                            headers=headers,
                            preload_content=False,
                        )
                    # Pass a `string` parameter directly in the body to support
                    # other content types than JSON when `body` argument is
                    # provided in serialized form.
                    elif isinstance(body, str) or isinstance(body, bytes):
                        headers = self._generate_request_signature(
                            method=method, url=url, headers=headers, body=body
                        )
                        r = self.pool_manager.request(
                            method,
                            url,
                            body=body,
                            timeout=timeout,
                            headers=headers,
                            preload_content=False,
                        )
                    elif headers["Content-Type"] == "text/plain" and isinstance(
                        body, bool
                    ):
                        request_body = "true" if body else "false"
                        headers = self._generate_request_signature(
                            method=method, url=url, headers=headers, body=request_body
                        )
                        r = self.pool_manager.request(
                            method,
                            url,
                            body=request_body,
                            preload_content=False,
                            timeout=timeout,
                            headers=headers,
                        )
                    else:
                        # Cannot generate the request from given parameters
                        msg = """Cannot prepare a request message for provided
                                         arguments. Please check that your arguments match
                                         declared content type."""
                        raise ApiException(status=0, reason=msg)
                # For `GET`, `HEAD`
                else:
                    headers = self._generate_request_signature(
                        method=method, url=url, headers=headers
                    )
                    r = self.pool_manager.request(
                        method,
                        url,
                        fields={},
                        timeout=timeout,
                        headers=headers,
                        preload_content=False,
                    )
            except urllib3.exceptions.SSLError as e:
                msg = "\n".join([type(e).__name__, str(e)])
                raise ApiException(status=0, reason=msg)

            return RESTResponse(r)


class PatchedApiClient(ApiClient):
    def __init__(
        self,
        api_secret,
        configuration=None,
        header_name=None,
        header_value=None,
        cookie=None,
    ):
        super(PatchedApiClient, self).__init__(
            configuration=configuration,
            header_name=header_name,
            header_value=header_value,
            cookie=cookie,
        )
        self.rest_client = _PatchedRestClient(
            api_secret=api_secret, configuration=configuration
        )
