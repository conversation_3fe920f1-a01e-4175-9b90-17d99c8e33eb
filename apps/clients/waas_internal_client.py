import logging
from typing import Dict, Optional

from django.conf import settings

from apps.clients.patched import PatchedApi<PERSON>lient
from apps.waas.client.generated.waas2_client import ApiClient, Configuration

logger = logging.getLogger("apps.client")


class WaaSInternalApiClientManager(object):
    _web_clients: Dict[str, ApiClient] = {}
    _dev_clients: Dict[str, ApiClient] = {}

    @classmethod
    def get_web_client(cls, org_id: Optional[str] = None) -> ApiClient:
        from app_libs.auth.managers.org_token import OrgTokenManager

        access_token = OrgTokenManager.get_token(org_id=org_id) if org_id else None
        api_secret = settings.PORTAL_APP_PRIVATE_KEY
        client = cls._web_clients.get(org_id)
        if not client or client.configuration.access_token != access_token:
            logger.info(f"Initializing WaaS Web API client with org_id: {org_id}")
            configuration = Configuration(
                host=settings.PORTAL_WEB_API_HOST, access_token=access_token
            )
            if not client:
                cls._web_clients[org_id] = PatchedApiClient(
                    api_secret=api_secret,
                    configuration=configuration,
                )
            else:
                cls._web_clients[org_id].rest_client._api_secret = api_secret
                cls._web_clients[org_id].configuration = configuration
            logger.info("WaaS Web API client initialized successfully")

        return cls._web_clients[org_id]

    @classmethod
    def get_dev_api_client(cls, org_id: Optional[str] = None) -> ApiClient:
        from app_libs.auth.managers.org_token import OrgTokenManager

        org_token = OrgTokenManager.get_token(org_id=org_id) if org_id else None
        access_token = org_token
        api_secret = settings.PORTAL_APP_PRIVATE_KEY
        client = cls._dev_clients.get(org_id)
        if not client or client.configuration.access_token != access_token:
            logger.info(f"Initializing WaaS Dev API client with org_id: {org_id}")
            configuration = Configuration(
                host=settings.PORTAL_DEV_API_HOST, access_token=access_token
            )
            if not client:
                cls._dev_clients[org_id] = PatchedApiClient(
                    api_secret=api_secret,
                    configuration=configuration,
                )
            else:
                cls._dev_clients[org_id].rest_client._api_secret = api_secret
                cls._dev_clients[org_id].configuration = configuration
            logger.info("WaaS Dev API client initialized successfully")

        return cls._dev_clients[org_id]
