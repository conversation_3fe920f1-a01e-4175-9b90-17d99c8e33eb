from typing import Dict, <PERSON><PERSON>

from cobo_app_python_api import (
    ApiClient,
    Configuration,
    MFAApi,
    MFAListResponse,
    MFAVerifyResponse,
)
from django.conf import settings


class CoboAppApiClientManager:
    _CLIENTS: Dict[str, Tuple[str, ApiClient]] = {}
    _org_id: str

    def __init__(self, org_id: str):
        self._org_id = org_id

    def _generate_client(self, access_token: str) -> ApiClient:
        configuration = Configuration(
            host=settings.PORTAL_APP_API_HOST,
            api_key=settings.PORTAL_APP_PRIVATE_KEY,
        )
        client = ApiClient(configuration=configuration)
        client.set_default_header("AUTHORIZATION", f"Bearer {access_token}")
        return client

    def _get_client(self) -> ApiClient:
        from app_libs.auth.managers.org_token import OrgTokenManager

        org_token = OrgTokenManager.get_token(org_id=self._org_id)
        api_key = settings.PORTAL_APP_PRIVATE_KEY
        client_tuple = self._CLIENTS.get(self._org_id)
        if not client_tuple:
            configuration = Configuration(
                host=settings.PORTAL_APP_API_HOST,
                api_key=api_key,
            )
            client = ApiClient(configuration=configuration)
            self._CLIENTS[self._org_id] = (org_token, client)
            client.set_default_header("AUTHORIZATION", f"Bearer {org_token}")
        elif client_tuple[0] != org_token:
            client = self._CLIENTS[self._org_id][1]
            client.configuration.api_key = api_key
            self._CLIENTS[self._org_id] = (org_token, client)
            client.set_default_header("AUTHORIZATION", f"Bearer {org_token}")

        return self._CLIENTS[self._org_id][1]

    def apps_mfa_list(
        self,
        user_id: int,
    ) -> MFAListResponse:
        with self._get_client() as api_client:
            api_instance = MFAApi(api_client)
            return api_instance.app_v2_mfa_mfa_list_retrieve(user_id=user_id)

    def apps_mfa_verify(
        self,
        mfa_uuid: str,
    ) -> MFAVerifyResponse:
        with self._get_client() as api_client:
            api_instance = MFAApi(api_client)
            return api_instance.app_v2_mfa_verify_retrieve(mfa_uuid=mfa_uuid)
