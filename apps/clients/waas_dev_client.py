import logging
from typing import Optional

from cobo_waas2 import ApiClient, Configuration, rest
from django.conf import settings

logger = logging.getLogger("apps.client")


class WrappedApiClient(ApiClient):
    def call_api(
        self,
        method,
        url,
        header_params=None,
        body=None,
        post_params=None,
        _request_timeout=None,
    ) -> rest.RESTResponse:
        if not _request_timeout:
            _request_timeout = settings.WAAS_REQUEST_TIMEOUT
        return super().call_api(
            method, url, header_params, body, post_params, _request_timeout
        )


class WaaSDevApiClientManager(object):
    _clients: dict[str, ApiClient] = {}

    @classmethod
    def get_client(
        cls, org_id: Optional[str] = None, app_id: Optional[str] = None
    ) -> ApiClient:
        from app_libs.auth.managers.org_token import OrgTokenManager

        org_token = OrgTokenManager.get_token(org_id=org_id) if org_id else None
        access_token = org_token
        api_private_key = settings.PORTAL_APP_PRIVATE_KEY
        client = cls._clients.get(org_id)
        if not client or client.configuration.access_token != access_token:
            logger.info(f"Initializing WaaS Dev API client with org_id: {org_id}")
            configuration = Configuration(
                host=settings.PORTAL_DEV_API_HOST,
                api_private_key=api_private_key,
                access_token=access_token,
            )
            if not client:
                cls._clients[org_id] = WrappedApiClient(
                    configuration=configuration,
                )
            else:
                cls._clients[org_id].configuration = configuration
            if access_token:
                cls._clients[org_id].set_default_header(
                    "Authorization", f"Bearer {access_token}"
                )
            logger.info("WaaS Web API client initialized successfully")

        return cls._clients[org_id]
