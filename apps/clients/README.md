# Portal Tokenization App - clients 模块

## 模块定位

clients 模块提供各种外部和内部服务的客户端实现，用于系统与其他服务的通信。这些客户端封装了底层API调用逻辑，为上层业务提供简单易用的接口。

## WaaS 客户端

系统中包含两种不同的 WaaS (Wallet-as-a-Service) 客户端实现：

### 1. waas_dev_client.py

- **来源**：通过 `requirements.txt` 中的 `cobo-waas2-python-api` 依赖引入
- **用途**：连接到外部 WaaS 开发环境API
- **特点**：使用官方SDK，适用于开发和测试环境

```python
# 使用示例
from apps.clients.waas_dev_client import WaaSDevApiClientManager

# 获取客户端实例
client = WaaSDevApiClientManager.get_client(org_id="your_org_id")

# 使用客户端调用API
# ...
```

### 2. waas_internal_client.py

- **来源**：通过 `apps.waas` 模块自动生成
- **用途**：连接到内部 WaaS 服务API
- **特点**：
    - 基于 OpenAPI 定义文件自动生成
    - 提供两种客户端：Web API客户端和开发API客户端
    - 完全内部使用，与公共API隔离

```python
# 使用示例
from apps.clients.waas_internal_client import WaaSInternalApiClientManager

# 获取Web API客户端
web_client = WaaSInternalApiClientManager.get_web_client(org_id="your_org_id")

# 获取开发API客户端
dev_client = WaaSInternalApiClientManager.get_dev_api_client(org_id="your_org_id")

# 使用客户端调用API
# ...
```

## 工作原理

```mermaid
graph TD
    A[业务逻辑] --> B[clients模块]
    B --> C1[waas_dev_client]
    B --> C2[waas_internal_client]
    C1 --> D1[外部WaaS服务]
    C2 --> D2[内部WaaS服务]
    E[waas模块] -.生成.-> C2
    F[cobo-waas2-python-api] -.提供.-> C1
```

## 认证机制

两种客户端都实现了 `_UnauthorizedRetryClient` 包装类，提供以下功能：

1. **自动重试** - 当遇到认证失败（401）时，会尝试刷新认证凭据并重试请求
2. **透明代理** - 对调用方隐藏认证细节，提供无缝的API访问体验
3. **异常处理** - 适当处理和转换API调用过程中的异常

## 开发注意事项

1. **客户端选择** - 根据实际需求选择合适的客户端：
    - 需要访问外部WaaS系统：使用 `waas_dev_client.py`
    - 需要访问内部WaaS系统：使用 `waas_internal_client.py`

2. **依赖管理** - 确保关联依赖正确安装：
    - `waas_dev_client.py` 依赖 `cobo-waas2-python-api` 包
    - `waas_internal_client.py` 依赖 `apps.waas` 模块生成的代码

3. **配置设置** - 确保相关环境变量和配置参数已正确设置：
    - 外部客户端：API密钥和端点URL
    - 内部客户端：PORTAL_WEB_API_HOST、PORTAL_DEV_API_HOST 等

4. **错误处理** - 调用API时应适当处理可能的异常，特别是网络错误或认证问题

