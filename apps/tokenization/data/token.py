from decimal import Decimal
from typing import Optional

from pydantic import BaseModel


class CacheTokenInfo(BaseModel):
    token_id: str
    chain_id: str
    decimal: int
    name: str = ""
    asset_id: str = ""
    fee_token_id: str = ""
    symbol: str = ""
    icon_url: str = ""
    token_address: str = ""
    usd_rate: Decimal = Decimal(0)
    dust_threshold: Optional[int]
    minimum_deposit_threshold: Optional[int]
    require_memo: bool = False


class TokenInfo(CacheTokenInfo):
    chain: CacheTokenInfo
