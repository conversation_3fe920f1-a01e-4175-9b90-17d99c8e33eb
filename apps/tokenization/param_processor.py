import logging
from decimal import Decimal
from typing import Any, Callable, Dict, Optional

from apps.tokenization.utils import decimal_to_string
from apps.utils.decimal_utils import DecimalUtils

logger = logging.getLogger("tokenization")

# 角色哈希值到角色名称的映射
ROLE_MAPPING = {
    "0x9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a6": "Minter",
    "0x3c11d16cbaffd01df69ce1c404f6340ee057498f5f00246190ea54220576a848": "Burner",
    "0x65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a": "Pauser",
    "0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08": "Manager",
    "0x9e16e6c596d2cf298b0b08786bf3a653eb1a9f723a5a76b814e3fbaa4f944609": "Salvager",
    "0x189ab7a9244df0848122154315af71fe140f3db0fe014031783b0946b8c9d2e3": "Upgrader",
    "0x0000000000000000000000000000000000000000000000000000000000000000": "Admin",
}

# 参数处理函数类型
ParamHandler = Callable[[Any, int], Any]


def handle_amount(value: Any, decimals: int) -> Decimal:
    """
    处理金额参数，将链上整数金额转换为人类可读的浮点数表示

    Args:
        value: 链上金额
        decimals: 代币小数位数

    Returns:
        处理后的Decimal对象
    """
    try:
        # 将整数金额转换为浮点数表示
        return DecimalUtils.int_to_float(value, decimal=decimals)
    except Exception as e:
        logger.error(f"处理金额参数失败: {e}")
        return value


def handle_role(value: Any, _: int) -> str:
    """
    处理角色参数，将bytes32哈希转换为可读的角色名称

    Args:
        value: 角色哈希值(bytes32或hex字符串)
        _: 占位参数，保持接口一致

    Returns:
        可读的角色名称
    """
    try:
        # 标准化角色哈希值为字符串格式
        role_hex = None

        # 如果是以0x开头的hex字符串
        if isinstance(value, str) and value.startswith("0x"):
            role_hex = value.lower()
        # 如果是bytes对象
        elif isinstance(value, bytes):
            role_hex = "0x" + value.hex().lower()
        else:
            return str(value)

        # 从映射表中查找角色名称
        role_name = ROLE_MAPPING.get(role_hex)
        if role_name:
            return role_name

        # 如果找不到匹配的角色名称，返回原始hex字符串
        return role_hex
    except Exception as e:
        logger.error(f"处理角色参数失败: {e}")
        return str(value)


# 参数处理映射: selector -> {param_index: handler_function}
PARAM_HANDLERS = {
    # mint(address,uint256)
    "40c10f19": {1: handle_amount},
    # burn(uint256)
    "42966c68": {0: handle_amount},
    # burnFrom(address,uint256)
    "79cc6790": {1: handle_amount},
    # transfer(address,uint256)
    "a9059cbb": {1: handle_amount},
    # transferFrom(address,address,uint256)
    "23b872dd": {2: handle_amount},
    # approve(address,uint256)
    "095ea7b3": {1: handle_amount},
    # grantRole(bytes32,address)
    "2f2ff15d": {0: handle_role},
    # revokeRole(bytes32,address)
    "d547741f": {0: handle_role},
    # renounceRole(bytes32,address)
    "36568abe": {0: handle_role},
    # hasRole(bytes32,address)
    "91d14854": {0: handle_role},
    # getRoleAdmin(bytes32)
    "248a9ca3": {0: handle_role},
}


def process_params_by_selector(
    selector: str, params: Dict[str, Any], decimals: int
) -> Dict[str, Any]:
    """
    根据selector处理参数

    Args:
        selector: 函数selector
        params: 原始参数字典
        decimals: 代币小数位数

    Returns:
        处理后的参数字典
    """
    if not params:
        return params

    # 创建参数的副本，避免修改原始数据
    processed_params = params.copy()

    # 特殊处理multicall函数
    if selector and selector.lower() == "ac9650d8" and "data" in processed_params:
        # multicall函数包含多个子调用
        data = processed_params["data"]
        if isinstance(data, list):
            processed_data = []
            for item in data:
                if isinstance(item, dict) and "function" in item and "params" in item:
                    # 直接使用子调用的selector，不再通过function name查找
                    sub_selector = item.get("selector")

                    if not sub_selector:
                        # 如果没有selector信息，记录警告但继续处理
                        logger.warning(f"No selector found for sub-call: {item}")
                        processed_data.append(item)
                        continue

                    # 递归处理子调用的参数
                    processed_sub_params = process_params_by_selector(
                        sub_selector, item["params"], decimals
                    )
                    processed_data.append(
                        {
                            "function": item["function"],
                            "params": processed_sub_params,
                            "selector": sub_selector,
                        }
                    )
                else:
                    processed_data.append(item)
            processed_params["data"] = processed_data
            return processed_params

    # 1. 首先应用特定参数处理器
    # 标准化selector格式 - 移除0x前缀并转为小写
    normalized_selector = selector.lower().replace("0x", "") if selector else None

    if normalized_selector and normalized_selector in PARAM_HANDLERS:
        param_handlers = PARAM_HANDLERS.get(normalized_selector)
        param_names = list(processed_params.keys())
        param_values = list(processed_params.values())

        for idx, handler in param_handlers.items():
            if idx < len(param_values):
                param_values[idx] = handler(param_values[idx], decimals)

        # 重建参数字典
        processed_params = {
            name: value for name, value in zip(param_names, param_values)
        }

    # 2. 然后处理所有数字类型参数 - 这是基础处理，确保所有数值都转为字符串
    processed_params = process_number_params(processed_params)

    return processed_params


def process_number_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理所有数字类型参数，将其转换为字符串以避免前后端传输误差

    Args:
        params: 参数字典

    Returns:
        处理后的参数字典
    """
    result = {}
    for key, value in params.items():
        if isinstance(value, (int, float, Decimal)):
            # 对所有数字类型应用通用处理
            try:
                result[key] = decimal_to_string(value)
            except Exception as e:
                logger.error(f"处理数字参数失败: {e}")
                result[key] = str(value)
        else:
            result[key] = value
    return result


def extract_amount_from_function(
    selector: str, params: Dict[str, Any]
) -> Optional[Any]:
    """
    基于PARAM_HANDLERS配置，从函数参数中提取金额参数

    Args:
        selector: 函数selector
        params: 函数参数字典

    Returns:
        提取的金额值，如果没有金额参数则返回None
    """
    # 标准化selector格式
    normalized_selector = selector.lower().replace("0x", "") if selector else None

    if not normalized_selector or normalized_selector not in PARAM_HANDLERS:
        return None

    param_handlers = PARAM_HANDLERS.get(normalized_selector)
    param_names = list(params.keys())

    # 查找使用handle_amount处理器的参数
    for idx, handler in param_handlers.items():
        if handler == handle_amount and idx < len(param_names):
            param_name = param_names[idx]
            return params.get(param_name)

    return None


def get_selector_from_calldata(calldata: str) -> Optional[str]:
    """
    从calldata中提取selector

    Args:
        calldata: 交易的calldata

    Returns:
        提取的selector，如果提取失败则返回None
    """
    if not calldata or len(calldata) < 10:  # 0x + 8位选择器
        return None

    return calldata[2:10].lower()
