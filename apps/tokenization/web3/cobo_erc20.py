import logging
from typing import Dict, List

from web3 import Web3
from web3.exceptions import BadFunctionCallOutput, ContractLogicError

from apps.tokenization.web3.web3 import web3_instance
from apps.utils.CoboERC20 import COBO_ERC20_ABI

logger = logging.getLogger(__name__)


def _handle_call_error(func):
    """A decorator to catch and handle web3 call errors gracefully."""

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except (ContractLogicError, BadFunctionCallOutput, ValueError) as e:
            # These exceptions often indicate an issue with the contract or parameters.
            class_name = args[0].__class__.__name__
            contract_address = args[0].contract.address
            logger.error(
                f"Web3 call error in {class_name}.{func.__name__} for contract {contract_address}: {e}"
            )
            # Re-raise with a more informative message
            raise ConnectionError(
                f"Contract call failed. Please verify that the contract is deployed at {contract_address} "
                f"on the correct network and that your parameters are valid. Original error: {e}"
            ) from e

    return wrapper


class CoboErc20Caller:
    def __init__(
        self, chain_id: str, contract_address: str, abi: List[Dict] = COBO_ERC20_ABI
    ):
        self.web3 = web3_instance(chain_id)
        if not self.web3.is_connected():
            raise ConnectionError(
                f"Failed to connect to node for chain_id '{chain_id}'. Please check your RPC_URL configuration."
            )

        # Check if there is code at the address
        code = self.web3.eth.get_code(Web3.to_checksum_address(contract_address))
        if code == b"\x00" or code == b"":
            raise ValueError(
                f"No contract code found at address '{contract_address}' on chain '{chain_id}'."
            )

        self.contract = self.web3.eth.contract(
            address=Web3.to_checksum_address(contract_address), abi=abi
        )

    @_handle_call_error
    def get_burner_role(self) -> bytes:
        return self.contract.functions.BURNER_ROLE().call()

    @_handle_call_error
    def get_default_admin_role(self) -> bytes:
        return self.contract.functions.DEFAULT_ADMIN_ROLE().call()

    @_handle_call_error
    def get_manager_role(self) -> bytes:
        return self.contract.functions.MANAGER_ROLE().call()

    @_handle_call_error
    def get_minter_role(self) -> bytes:
        return self.contract.functions.MINTER_ROLE().call()

    @_handle_call_error
    def get_pauser_role(self) -> bytes:
        return self.contract.functions.PAUSER_ROLE().call()

    @_handle_call_error
    def get_salvager_role(self) -> bytes:
        return self.contract.functions.SALVAGER_ROLE().call()

    @_handle_call_error
    def get_upgrader_role(self) -> bytes:
        return self.contract.functions.UPGRADER_ROLE().call()

    @_handle_call_error
    def get_upgrade_interface_version(self) -> str:
        return self.contract.functions.UPGRADE_INTERFACE_VERSION().call()

    @_handle_call_error
    def get_access_list_enabled(self) -> bool:
        return self.contract.functions.accessListEnabled().call()

    @_handle_call_error
    def get_allowance(self, owner: str, spender: str) -> int:
        return self.contract.functions.allowance(
            Web3.to_checksum_address(owner), Web3.to_checksum_address(spender)
        ).call()

    @_handle_call_error
    def get_balance_of(self, account: str) -> int:
        return self.contract.functions.balanceOf(
            Web3.to_checksum_address(account)
        ).call()

    @_handle_call_error
    def get_contract_uri(self) -> str:
        return self.contract.functions.contractUri().call()

    @_handle_call_error
    def get_decimals(self) -> int:
        return self.contract.functions.decimals().call()

    @_handle_call_error
    def get_access_list(self) -> List[str]:
        return self.contract.functions.getAccessList().call()

    @_handle_call_error
    def get_block_list(self) -> List[str]:
        return self.contract.functions.getBlockList().call()

    @_handle_call_error
    def get_role_admin(self, role: bytes) -> bytes:
        return self.contract.functions.getRoleAdmin(role).call()

    @_handle_call_error
    def get_has_role(self, role: bytes, account: str) -> bool:
        return self.contract.functions.hasRole(
            role, Web3.to_checksum_address(account)
        ).call()

    @_handle_call_error
    def get_access_listed(self, account: str) -> bool:
        return self.contract.functions.isAccessListed(
            Web3.to_checksum_address(account)
        ).call()

    @_handle_call_error
    def get_block_listed(self, account: str) -> bool:
        return self.contract.functions.isBlockListed(
            Web3.to_checksum_address(account)
        ).call()

    @_handle_call_error
    def get_name(self) -> str:
        return self.contract.functions.name().call()

    @_handle_call_error
    def get_paused(self) -> bool:
        return self.contract.functions.paused().call()

    @_handle_call_error
    def get_proxiable_uuid(self) -> bytes:
        return self.contract.functions.proxiableUUID().call()

    @_handle_call_error
    def supports_interface(self, interface_id: bytes) -> bool:
        return self.contract.functions.supportsInterface(interface_id).call()

    @_handle_call_error
    def get_symbol(self) -> str:
        return self.contract.functions.symbol().call()

    @_handle_call_error
    def get_total_supply(self) -> int:
        return self.contract.functions.totalSupply().call()

    @_handle_call_error
    def get_version(self) -> int:
        return self.contract.functions.version().call()

    def get_is_access_listed(self) -> bool:
        return self.contract.functions.isAccessListed().call()

    def get_is_block_listed(self) -> bool:
        return self.contract.functions.isBlockListed().call()
