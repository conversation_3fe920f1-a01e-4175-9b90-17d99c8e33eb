from typing import Dict, List

from django.conf import settings
from web3 import Web3
from web3.contract import Contract
from web3.middleware import geth_poa_middleware


def web3_provider(chain_id: str) -> str:
    return settings.WEB3_PROVIDER_MAP.get(chain_id)


def web3_instance(chain_id: str, provider: str = None) -> Web3:
    if not provider:
        provider = web3_provider(chain_id)
    web3 = Web3(Web3.HTTPProvider(provider))
    web3.middleware_onion.inject(geth_poa_middleware, layer=0)
    web3.strict_bytes_type_checking = False
    return web3


def web3_contract(
    chain_id: str, contract_address: str, abi: List[Dict], provider: str = None
) -> Contract:
    return web3_instance(chain_id, provider).eth.contract(
        address=Web3.to_checksum_address(contract_address), abi=abi
    )
