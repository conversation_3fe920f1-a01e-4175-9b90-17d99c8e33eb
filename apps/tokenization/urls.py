"""
Tokenization应用的URL配置
"""

from django.urls import path

from .views.chain_views import ChainListView
from .views.token_management_views import (
    BlockCheckView,
    BlockListView,
    BlockUpdateView,
    BurnTokenView,
    EstimateOperationFeeView,
    MintTokenView,
    PauseTokenView,
    PermissionEditView,
    PermissionListView,
    RestrictedAccessView,
    UpdateTokenView,
    UserAddressListView,
    WalletBalanceView,
)
from .views.token_views import (
    BalanceView,
    ContractCallView,
    ContractEstimateFeeView,
    ContractReadView,
    OrgTokenListView,
    TokenCreateView,
    TokenDeleteView,
    TokenInfoView,
    TransactionDetailView,
    TransactionHistoryView,
)

urlpatterns = [
    # 新增的Token Management API
    path("v1/token/list", OrgTokenListView.as_view(), name="org_token_list"),
    path("v1/token/update/", UpdateTokenView.as_view(), name="token_update"),
    path("v1/token/mint/", MintTokenView.as_view(), name="token_mint"),
    path("v1/token/burn/", BurnTokenView.as_view(), name="token_burn"),
    path("v1/token/pause/", PauseTokenView.as_view(), name="token_pause"),
    path("v1/token/create/", TokenCreateView.as_view(), name="token_create"),
    path("v1/token/delete/", TokenDeleteView.as_view(), name="token_delete"),
    path("v1/token/balance/", BalanceView.as_view(), name="token_balance"),
    # block Management API
    path("v1/block/list/", BlockListView.as_view(), name="block_list"),
    path("v1/block/update/", BlockUpdateView.as_view(), name="block_add"),
    path("v1/block/check/", BlockCheckView.as_view(), name="block_check"),
    # Permission Management API
    path("v1/permission/list/", PermissionListView.as_view(), name="permission_list"),
    path("v1/permission/edit/", PermissionEditView.as_view(), name="permission_edit"),
    # Restricted Access API
    path(
        "v1/token/restricted-access/",
        RestrictedAccessView.as_view(),
        name="token_restricted_access",
    ),
    path(
        "v1/token/token-info/",
        TokenInfoView.as_view(),
        name="chain_info_view",
    ),
    path(
        "v1/operation/estimate-fee/",
        EstimateOperationFeeView.as_view(),
        name="operation_estimate_fee",
    ),
    path("v1/address/list/", UserAddressListView.as_view(), name="user_address_list"),
    path(
        "v1/transaction/list", TransactionHistoryView.as_view(), name="transaction_list"
    ),
    path(
        "v1/transaction/detail",
        TransactionDetailView.as_view(),
        name="transaction_detail",
    ),
    path("v1/wallet/balance/", WalletBalanceView.as_view(), name="wallet_balance"),
    # 链管理
    path("v1/chain/list", ChainListView.as_view(), name="chain_list"),
    path(
        "v1/contract/estimate-fee/",
        ContractEstimateFeeView.as_view(),
        name="contract-estimate-fee",
    ),
    path("v1/contract/call/", ContractCallView.as_view(), name="contract-call"),
    path("v1/contract/read/", ContractReadView.as_view(), name="contract-read"),
]
