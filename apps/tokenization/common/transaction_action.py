from apps.utils.enums import StrEnum


class TransactionActionType(StrEnum):
    # Basic Operation
    CREATE_TOKEN = "Create Token"
    MINT = "Mint"
    BURN = "Burn"
    PAUSE = "Pause"
    UNPAUSE = "Unpause"
    ENABLE_RESTRICTED_ACCESS = "Enable Restricted Access"
    DISABLE_RESTRICTED_ACCESS = "Disable Restricted Access"
    ADD_ALLOWED_ADDRESS = "Add Allowed Address"
    DELETE_ALLOWED_ADDRESS = "Delete Allowed Address"
    ADD_BLOCKED_ADDRESS = "Add Blocked Address"
    DELETE_BLOCKED_ADDRESS = "Delete Blocked Address"
    GRANT_PERMISSION = "Grant Permission"
    EDIT_PERMISSION = "Edit Permission"
    DELETE_PERMISSION = "Delete Permission"
    # EVM Specific Opertions
    RAW_CONTRACT_CALL = "Raw Contract Call"
    # SOL Specific Opertions
    AUTHORIZE_SPECIFIC_ADDRESS = "Authorize Specific Address"
    FORCE_BURN = "Force Burn"
    FORCE_TRANSFER = "Force Transfer"
    UPDATE_METADATA = "Update Metadata"
