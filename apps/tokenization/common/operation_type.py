from apps.utils.enums import StrEnum


class TokenOperationType(StrEnum):
    """代币操作类型"""

    # cobo erc20 more actions
    GRANT_ROLE = "grantRole"  # 授予角色
    REVOKE_ROLE = "revokeRole"  # 撤销角色
    RENOUNCE_ROLE = "renounceRole"  # 放弃角色
    ACCESS_LIST_ADD = "accessListAdd"  # 添加白名单
    ACCESS_LIST_REMOVE = "accessListRemove"  # 移除白名单
    BLOCK_LIST_ADD = "blockListAdd"  # 添加黑名单
    BLOCK_LIST_REMOVE = "blockListRemove"  # 移除黑名单
    TOGGLE_ACCESSLIST = "toggleAccesslist"  # 开关白名单功能
    MINT = "mint"  # 铸币
    BURN = "burn"  # 销毁
    BURN_FROM = "burnFrom"  # 从指定地址销毁
    PAUSE = "pause"  # 暂停
    UNPAUSE = "unpause"  # 恢复
    CONTRACT_URI_UPDATE = "contractUriUpdate"  # 更新合约URI
    UPGRADE_TO_AND_CALL = "upgradeToAndCall"  # 升级合约
    SALVAGE_ERC20 = "salvageERC20"  # 救援ERC20代币
    SALVAGE_NATIVE = "salvageNative"  # 救援原生代币
    MULTICALL = "multicall"
    APPROVE = "approve"  # 批准
    TRANSFER = "transfer"  # 转账
    TRANSFER_FROM = "transferFrom"  # 从指定地址转账

    # sol more actions
    BURN_CHECKED = "burnChecked"
    TRANSFER_CHECKED = "transferChecked"
    UPDATE_FIELD = "updateField"
