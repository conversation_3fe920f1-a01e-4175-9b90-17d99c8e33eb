import decimal
from typing import Callable, List, Optional, Tuple, Union

from django.conf import settings

DEFAULT_BATCH_PAGE_SIZE = 50
ctx = decimal.Context()


def paginated_request(
    api_func: Callable,
    limit: int = DEFAULT_BATCH_PAGE_SIZE,
    _request_timeout: Optional[Union[float, Tuple[float, float]]] = None,
    **kwargs,
) -> List:
    """
    Generic helper for handling paginated API requests

    Args:
        api_func: API function to call
        limit: Page size limit
        _request_timeout: Optional timeout
        **kwargs: Additional parameters to pass to API function
    """
    if not _request_timeout:
        _request_timeout = settings.WAAS_REQUEST_TIMEOUT
    all_results = []
    after = None
    has_more = True

    while has_more:
        response = api_func(
            limit=limit, after=after, _request_timeout=_request_timeout, **kwargs
        )

        if response.data:
            all_results.extend(response.data)

        if response.pagination and response.pagination.after:
            assert response.pagination.after != after
            after = response.pagination.after
        else:
            has_more = False

    return all_results


def decimal_to_string(
    decimal_value,
    decimal_places: Optional[int] = None,
    rounding_mode: Optional[str] = None,
):
    if isinstance(decimal_value, int):
        return str(decimal_value)
    elif isinstance(decimal_value, float):
        # 将浮点数先转换为 Decimal 再处理，避免浮点数不准确的问题
        decimal_value = ctx.create_decimal(repr(decimal_value))
    elif isinstance(decimal_value, decimal.Decimal):
        decimal_value = decimal_value.normalize()
    else:
        raise TypeError(
            f"Unsupported type: {type(decimal_value)}. Expected int, float, or Decimal."
        )

    if decimal_places is not None:
        quantize_exp = decimal.Decimal(f"1.{'0' * decimal_places}")
        decimal_value = decimal_value.quantize(
            quantize_exp, rounding=rounding_mode
        ).normalize()

    return format(decimal_value, "f")


def decimal_to_usd_value(decimal_value):
    return decimal_to_string(
        decimal_value, decimal_places=2, rounding_mode=decimal.ROUND_HALF_UP
    )
