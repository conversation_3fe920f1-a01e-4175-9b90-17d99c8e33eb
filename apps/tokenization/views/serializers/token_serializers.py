"""
 API 序列化器
定义请求和响应的数据结构
"""
from typing import Any, Dict, List, Optional

from cobo_app_python_api.models.app_address_data import AppAddressData
from cobo_waas2 import (
    AddressInfo,
    Pagination,
    TokenizationAddressPermission,
    TokenizationAllowlistActivationRequest,
    TokenizationBurnTokenRequest,
    TokenizationEstimateFeeRequestOperationParams,
    TokenizationHoldingInfo,
    TokenizationMintTokenRequest,
    TokenizationPauseTokenRequest,
    TokenizationTokenInfo,
    TokenizationTokenOperationSource,
    TokenizationTokenPermissionType,
    TokenizationTokenStandard,
    TokenizationUpdateAllowlistAddressesRequest,
    TokenizationUpdateBlocklistAddressesRequest,
    TransactionDetail,
    TransactionFee,
    TransactionRequestFee,
    TransactionStatus,
    WalletSubtype,
)
from cobo_waas2.models.wallet_type import WalletType
from pydantic import BaseModel, Field

from apps.tokenization.common.block_type import BlockType
from apps.tokenization.common.operation_type import TokenOperationType
from apps.tokenization.common.pause_type import PauseType
from apps.tokenization.common.transaction_action import TransactionActionType


# Schema兼容处理, 预防cobo_waas2 schema变化对接口的冲击
class BurnTokenizationRequest(TokenizationBurnTokenRequest):
    pass


class MintTokenizationRequest(TokenizationMintTokenRequest):
    pass


class PauseTokenizationRequest(TokenizationPauseTokenRequest):
    pass


class UpdateAllowlistAddressesRequest(TokenizationUpdateAllowlistAddressesRequest):
    pass


class UpdateTokenizationAllowlistActivationRequest(
    TokenizationAllowlistActivationRequest
):
    pass


class UpdateTokenizationBlocklistAddressesRequest(
    TokenizationUpdateBlocklistAddressesRequest
):
    pass


class TokenizationBlocklistAddressNote(BaseModel):
    address: str = Field(..., description="Address")
    note: str = Field(default="", description="Note")
    created_timestamp: int = Field(..., description="Created timestamp")
    is_blocked: bool = Field(
        False,
        description="Whether the address is blocked",
    )


# 通用交易详情基类
class BaseTxDetail(BaseModel):
    """Base transaction detail"""

    source: TokenizationTokenOperationSource = Field(
        ..., description="Operation source"
    )


class BaseContractModel(BaseModel):
    token_id: str = Field(..., description="Token ID for the operation")


class CreateTokenRequest(BaseTxDetail):
    """Create token request parameters"""

    est_fee: TransactionRequestFee = Field(..., description="Estimated fee")
    chain: str = Field(..., description="Chain name")
    name: str = Field(..., description="Token name", max_length=100)
    symbol: str = Field(..., description="Token symbol", max_length=20)
    decimals: int = Field(18, description="Token decimals", ge=0, le=18)
    all_permission: bool = Field(True, description="Grant all token permission")
    admin: Optional[str] = Field("", description="Admin wallet address")
    minter: Optional[str] = Field("", description="Minter wallet address")
    manager: Optional[str] = Field("", description="Manager wallet address")
    permanent_delegate: Optional[str] = Field(
        "", description="Permanent delegate wallet address"
    )
    freezer: Optional[str] = Field("", description="Freezer wallet address")
    updater: Optional[str] = Field("", description="Updater wallet address")
    pauser: Optional[str] = Field("", description="Pauser wallet address")
    standard: Optional[TokenizationTokenStandard] = Field(
        "", description="Standard tokenization"
    )
    allowlist_activated: Optional[bool] = Field(
        False, description="Allowlist activated"
    )
    app_initiator: Optional[str] = Field(
        default=None,
        description="The initiator of the tokenization activity. If you do not specify this property, the WaaS service will automatically designate the API key as the initiator.",
    )


class DeleteTokenRequest(BaseContractModel):
    pass


class TokenInfo(BaseModel):
    """Token detail"""

    token: TokenizationTokenInfo = Field(..., description="Token info")
    chain_icon_url: Optional[str] = Field(..., description="Chain icon URL")
    token_icon_url: Optional[str] = Field(..., description="Token icon URL")
    scan_url: Optional[str] = Field(..., description="Scan URL")
    scan_icon_url: Optional[str] = Field(..., description="Scan icon URL")


class GetTokenParam(BaseModel):
    keyword: str = Field(..., description="Filter keyword")
    status: str = Field(..., description="Active | Processing | Failed | Pausing")


class TokenInfoRequest(BaseContractModel):
    """查询代币信息请求参数"""


class BalanceRequest(BaseContractModel):
    """查询余额请求参数"""


class TransactionFiler(BaseModel):
    status: Optional[str] = Field(
        None,
        description="A list of transaction statuses, separated by comma. Possible values include:    - `Submitted`: The transaction is submitted.   - `PendingScreening`: The transaction is pending screening by Risk Control.    - `PendingAuthorization`: The transaction is pending approvals.   - `PendingSignature`: The transaction is pending signature.    - `Broadcasting`: The transaction is being broadcast.   - `Confirming`: The transaction is waiting for the required number of confirmations.   - `Completed`: The transaction is completed.   - `Failed`: The transaction failed.   - `Rejected`: The transaction is rejected.   - `Pending`: The transaction is waiting to be included in the next block of the blockchain. ",
    )
    type: Optional[TransactionActionType] = Field(None, description="Type")
    creat_time_before: Optional[int] = Field(
        None, description="Created time before (timestamp)"
    )
    creat_time_after: Optional[int] = Field(
        None, description="Created time after (timestamp)"
    )


class TransactionHistoryRequest(BaseContractModel):
    """Transaction history request parameters"""

    decimals: int = Field(18, description="Token decimals")
    keyword: Optional[str] = Field(None, description="Keyword for search")
    filter: Optional[TransactionFiler] = Field(None, description="Filter object")
    current: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Page size")
    direction: Optional[str] = Field(
        "DESC", description="Sort direction: ASC (ascending), DESC (descending)"
    )


class TransactionDetailsRequest(BaseModel):
    """Transaction details request parameters"""

    transaction_id: str = Field(..., description="Transaction ID")


class TransactionDetailResponse(BaseModel):
    """Transaction details response"""

    detail: TransactionDetail = Field(..., description="Transaction detail")
    tx_hash_scan_url: Optional[str] = Field(
        None, description="Transaction hash scan URL"
    )


class PermissionConfig(BaseModel):
    admin: str = Field(..., description="Admin address")
    minter: str = Field(..., description="Minter address")
    pauser: str = Field(..., description="Pauser address")


class DeleteTokenParam(BaseModel):
    id: str = Field(..., description="Token ID")


class ListTokenResponse(BaseModel):
    pagination: Pagination
    data: List[TokenInfo]


class BalanceDetail(BaseModel):
    balance: str = Field(..., description="Balance (formatted, e.g. 123.45)")
    wallet_address: str = Field(..., description="Wallet address")
    wallet_name: str = Field(..., description="Wallet name")
    wallet_id: str = Field(..., description="Wallet ID")


class BalanceResponse(BaseModel):
    """Token balance response data"""

    balance: List[TokenizationHoldingInfo] = Field(
        ..., description="Token balance detail list"
    )


class TransferRequest(BaseModel):
    """Token transfer request parameters"""

    contract_address: str = Field(..., description="Contract address")
    from_address: str = Field(..., description="Sender address")
    to_address: str = Field(..., description="Recipient address")
    amount: str = Field(..., description="Transfer amount (formatted, e.g. 123.45)")
    chain: str = Field(..., description="Chain name")
    private_key: Optional[str] = Field(
        None,
        description="Private key (for testing only, use secure method in production)",
    )


class TransferResponse(BaseModel):
    """Token transfer response data"""

    transaction_hash: str = Field(..., description="Transaction hash")
    from_address: str = Field(..., description="Sender address")
    to_address: str = Field(..., description="Recipient address")
    amount: str = Field(..., description="Transfer amount (formatted, e.g. 123.45)")
    contract_address: str = Field(..., description="Contract address")
    chain: str = Field(..., description="Chain name")
    status: str = Field(..., description="Transaction status")


class Timeline(BaseModel):
    """Timeline event"""

    type: str = Field(description="Timeline event type")
    status: str = Field(description="Timeline event status")
    finished_timestamp: Optional[int] = Field(
        description="Event finished timestamp (ms)"
    )


class TransactionRecord(BaseModel):
    """Transaction record"""

    hash: str = Field(..., description="Transaction hash")
    initiated_address: str = Field(..., description="Sender address", alias="from")
    target_address: str = Field(..., description="Recipient address")
    amount: str = Field(..., description="Amount (formatted, e.g. 123.45)")
    timestamp: int = Field(..., description="Timestamp")
    block_number: int = Field(..., description="Block number")
    status: str = Field(..., description="Transaction status")
    type: str = Field(..., description="Transaction type")
    create_time: int = Field(..., description="Timestamp when the activity was created")
    update_time: int = Field(
        ..., description="Timestamp when the activity was last updated"
    )
    transaction_id: str = Field(..., description="Transaction ID")
    request_id: str = Field(..., description="Request ID")
    cobo_id: str = Field(..., description="Cobo system ID")
    initiator: str = Field(..., description="Initiator")
    expiration_time: int = Field(..., description="Expiration timestamp")
    initiating_wallet: str = Field(..., description="Initiating wallet address")
    interact_with: str = Field(
        ..., description="Interaction target (e.g. contract or address)"
    )
    network_fee: str = Field(..., description="Network fee (formatted, e.g. 0.01)")
    timelines: List[Timeline] = Field(..., description="Timeline event list")


class PaginationInfo(BaseModel):
    """Pagination info"""

    current: int = Field(..., description="Current Page")
    page_size: int = Field(..., description="Page size")
    total_count: int = Field(..., description="Total record count")


class FunctionsData(BaseModel):
    type: str = Field(..., description="Function type")
    json: Dict[str, Any] = Field(..., description="Function JSON")


class Transaction(BaseModel):
    """Transaction detail"""

    type: TransactionActionType = Field(
        ...,
        description="Transaction type. Possible values: Deposit, Withdrawal, ContractCall, MessageSign, ExternalSafeTx, Stake, Unstake",
    )
    tx_hash: Optional[str] = Field(..., description="Transaction hash, unique on chain")
    status: Optional[TransactionStatus] = Field(
        ...,
        description="Transaction status. Possible values: Submitted, PendingScreening, PendingAuthorization, PendingSignature, Broadcasting, Confirming, Completed, Failed, Rejected, Pending",
    )
    initiated_address: Optional[str] = Field(..., description="Sender address")
    initiated_wallet: Optional[str] = Field(..., description="Sender wallet name")
    initiated_wallet_id: Optional[str] = Field(..., description="Sender wallet ID")
    target_address: Optional[str] = Field(..., description="Recipient address")
    target_wallet: Optional[str] = Field(..., description="Recipient wallet name")
    target_wallet_id: Optional[str] = Field(..., description="Recipient wallet ID")
    amount: Optional[str] = Field(
        ..., description="Transaction amount (formatted, e.g. 123.45)"
    )
    create_time: Optional[int] = Field(
        ..., description="Transaction creation timestamp (Unix ms)"
    )
    update_time: Optional[int] = Field(
        ..., description="Transaction last update timestamp (Unix ms)"
    )
    chain_icon: Optional[str] = Field(..., description="Chain icon")
    token_icon: Optional[str] = Field(..., description="Token icon")
    transaction_id: Optional[str] = Field(
        ..., description="Transaction ID (internal unique)"
    )
    request_id: Optional[str] = Field(
        ..., description="Request ID (provided by client)"
    )
    cobo_id: Optional[str] = Field(..., description="Cobo platform internal ID")
    initiator: Optional[str] = Field(
        ..., description="Transaction initiator identifier"
    )
    network_fee: Optional[TransactionFee] = Field(
        ..., description="Network fee (formatted, e.g. 0.01)"
    )
    functions: Optional[List[FunctionsData]] = Field(
        ..., description="Contract function call info list"
    )
    tx_hash_scan_url: Optional[str] = Field()


class TransactionHistoryResponse(BaseModel):
    """Transaction history response data"""

    transactions: Optional[List[Transaction]] = Field(
        ..., description="Transaction record list"
    )
    pagination: Optional[PaginationInfo] = Field(..., description="Pagination info")


"""
代币操作API序列化器
支持Deploy、Mint、Burn等多种操作类型
"""


class OperationParams(BaseTxDetail):
    """Operation parameters"""

    token_type: str = Field(..., description="Token type")
    access_mode: str = Field(..., description="Access mode")
    chain_id: str = Field(..., description="Chain ID")
    name: str = Field(..., description="Token name")
    symbol: str = Field(..., description="Token symbol")
    decimal: int = Field(..., description="Token decimals", ge=0, le=18)
    permission_type: str = Field(..., description="Permission type")
    permission_config: Optional[PermissionConfig] = Field(
        None, description="Permission config"
    )


class EstimateFeeRequest(BaseModel):
    """Token operation request parameters"""

    operation_params: TokenizationEstimateFeeRequestOperationParams = Field(
        ..., description="Operation parameters"
    )


class ChainFeeRequest(BaseModel):
    """Token operation request parameters"""

    chain_id: str = Field(..., description="Chain ID")


class TransferOperationParams(BaseTxDetail):
    """Transfer operation parameters"""

    token_address: str = Field(..., description="Token contract address")
    from_address: str = Field(..., description="Sender address", alias="from")
    to: str = Field(..., description="Recipient address")
    amount: str = Field(..., description="Transfer amount")


class EstimatedFeeParams(BaseTxDetail):
    """Token operation request parameters"""

    operation_type: str = Field(
        ..., description="Operation type: Deploy | Mint | Burn | .."
    )
    operation_params: OperationParams = Field(..., description="Operation parameters")


class OperationsListParam(BaseModel):
    token_id: str = Field(..., description="Token ID")
    status: Optional[str] = Field(None, description="Status")
    type: Optional[TokenizationTokenPermissionType] = Field(None, description="Type")
    sort_by: Optional[str] = Field(None, description="Sort by")
    sort_direction: Optional[str] = Field(None, description="ASC | DESC")


class InitialAddress(BaseModel):
    """Initiator address info"""

    wallet_type: str = Field(..., description="Wallet type")
    wallet_sub_type: str = Field(..., description="Wallet subtype")
    wallet_id: str = Field(..., description="Wallet ID")
    wallet_name_info: str = Field(..., description="Wallet name info")
    address: str = Field(..., description="Wallet address")
    label: str = Field(..., description="Address label")


class OperationDetail(BaseModel):
    """Operation detail"""

    id: str = Field(..., description="Operation ID")
    tx_hash: str = Field(..., description="Transaction hash")
    type: Optional[TokenizationTokenPermissionType] = Field(
        None, description="Operation type"
    )
    status: str = Field(
        None,
        description="Operation status: Broadcasting | Completed | Rejected | Failed",
    )
    create_time: int = Field(..., description="Creation timestamp")
    update_time: int = Field(..., description="Update timestamp")
    initiated_address: InitialAddress = Field(..., description="Initiator address info")
    target_address: str = Field(..., description="Target address")
    target_address_note: str = Field(..., description="Target address note")
    tx_amount: int = Field(..., description="Transaction amount")
    tx_token_id: str = Field(..., description="Transaction token ID")
    token_icon_url: str = Field(..., description="Token icon URL")
    fee: TransactionRequestFee = Field(..., description="Transaction fee")


class OperationsListResponse(BaseModel):
    """Operation list response"""

    pagination: PaginationInfo = Field(..., description="Pagination info")
    data: List[OperationDetail] = Field(..., description="Operation detail list")


class OperationDetailResponse(BaseModel):
    """Operation detail response"""

    transaction_id: str = Field(..., description="Transaction ID")
    requestId: str = Field(..., description="Request ID")
    coboId: str = Field(..., description="Cobo system ID")
    initiator: str = Field(..., description="Initiator")
    create_time: int = Field(..., description="Creation timestamp")
    updated_time: int = Field(..., description="Update timestamp")
    status: str = Field(None, description="Status: Broadcasting | Completed | Rejected")
    detail: OperationDetail = Field(..., description="Operation detail")
    timeline: List[Timeline] = Field(..., description="Timeline event list")


"""
新增代币管理API序列化器
包括更新代币信息、Mint、Burn、黑白名单管理、权限管理等功能
"""


class UpdateTokenTxDetail(BaseTxDetail):
    """Update token info transaction detail"""


class UpdateTokenRequest(BaseContractModel):
    """Update token info request parameters"""

    est_fee: TransactionRequestFee = Field(..., description="Estimated fee")
    tx_detail: UpdateTokenTxDetail = Field(..., description="Transaction detail")


class MintRequest(BaseContractModel):
    """Mint request parameters"""

    tx_detail: MintTokenizationRequest = Field(..., description="Transaction detail")


class BurnRequest(BaseContractModel):
    """Burn token request parameters"""

    est_fee: TransactionRequestFee = Field(..., description="Estimated fee")
    tx_detail: BurnTokenizationRequest = Field(..., description="Transaction detail")


class CommonOperationResponse(BaseModel):
    """Common operation response"""

    id: str = Field(..., description="Operation ID")


# 黑白名单相关序列化器
class BlockListRequest(BaseContractModel):
    """Query block/allow list address request parameters"""

    type: BlockType = Field(..., description="Address type: Block | Allow")


class AddressDetail(AddressInfo):
    """Address info"""


class BlockListResponse(BaseModel):
    """Address list response"""

    pagination: Optional[Pagination] = Field(None, description="Pagination info")
    data: List[TokenizationBlocklistAddressNote] = Field(
        ..., description="Address list"
    )


class DeleteBlockDetail(BaseTxDetail):
    """Delete address transaction detail"""

    type: BlockType = Field(..., description="Address type: Allowed | Block")
    target_address: str = Field(..., description="Target address")


class EmptyResponse(BaseModel):
    """Empty response"""

    tx_id: str = Field(..., description="Blockchain transaction ID")


class DeleteBlockRequest(BaseContractModel):
    """Delete address request parameters"""

    est_fee: TransactionRequestFee = Field(..., description="Estimated fee")
    tx_detail: DeleteBlockDetail = Field(..., description="Address detail")


# 权限管理相关序列化器
class PermissionListRequest(BaseContractModel):
    """Query permission list request parameters"""


class PermissionListResponse(BaseModel):
    """Permission list response"""

    data: Optional[List[TokenizationAddressPermission]] = Field(
        ..., description="Permission info list"
    )


class EditPermissionTxDetail(BaseTxDetail):
    """Edit permission transaction detail"""

    address: str = Field(..., description="Address")
    permission_list: List[str] = Field(
        ..., description="Permission list: [Manage, Mint, Burn, ...]"
    )


class PauseTokenRequest(BaseContractModel):
    """Pause/unpause token request parameters"""

    est_fee: TransactionRequestFee = Field(..., description="Estimated fee")
    tx_detail: PauseTokenizationRequest = Field(..., description="Transaction detail")
    type: PauseType = Field(..., description="Operation type: Pause | Unpause")


class RestrictedAccessRequest(BaseContractModel):
    """Query restricted access status request parameters"""


class RestrictedAccessResponse(BaseModel):
    """Query restricted access status response data"""

    switch: bool = Field(..., description="Restricted access switch status")


class UpdateRestrictedAccessRequest(BaseContractModel):
    """Update restricted access status request parameters"""

    tx_detail: UpdateTokenizationAllowlistActivationRequest = Field(
        ..., description="Restricted access switch status"
    )


class BlockUpdateRequest(BaseContractModel):
    """Add block/allow list address request parameters"""

    type: BlockType = Field(..., description="Address type")
    block: Optional[UpdateTokenizationBlocklistAddressesRequest] = Field(
        None, description="Block list"
    )
    allow: Optional[UpdateAllowlistAddressesRequest] = Field(
        None, description="Allow list"
    )



class PermissionOperation(BaseModel):
    """Permission operation detail"""

    operation: str = Field(..., description="Operation type: 'add' or 'remove'")
    address: str = Field(..., description="Target address")
    permissions: List[str] = Field(..., description="Permission list: [Admin, Mint, Burn, Pause, Manage, Salvage, Upgrade, Freeze, Close]")

    def __init__(self, **data):
        super().__init__(**data)
        # 验证操作类型
        if self.operation not in ["add", "remove"]:
            raise ValueError("Operation must be 'add' or 'remove'")

        # 验证权限类型
        valid_permissions = ["Admin", "Mint", "Burn", "Pause", "Manage", "Salvage", "Upgrade", "Freeze", "Close"]
        for permission in self.permissions:
            if permission not in valid_permissions:
                raise ValueError(f"Invalid permission: {permission}. Valid permissions are: {valid_permissions}")


class PermissionEditTxDetail(BaseTxDetail):
    """Edit permission transaction detail"""

    operations: List[PermissionOperation] = Field(
        ..., description="Permission operations to perform"
    )


class PermissionEditRequest(BaseContractModel):
    """Edit permission request parameters"""

    est_fee: TransactionRequestFee = Field(..., description="Estimated fee")
    tx_detail: PermissionEditTxDetail = Field(..., description="Permission edit detail")


class OrgTokenListResponse(BaseModel):
    """Organization token list response"""

    tokens: List[TokenInfo] = Field(..., description="Token list")


class BlockCheckRequest(BaseContractModel):
    """Address operation permission check request parameters"""

    address: str = Field(..., description="Address to check")


class BlockCheckResponse(BaseModel):
    """Address operation permission check response data"""

    allowed: bool = Field(..., description="Whether the address is allowed to operate")


class UserAddressListRequest(BaseModel):
    """User wallet address list request parameters"""

    chain_id: Optional[str] = Field(
        default="SETH", description="Chain ID for the operation"
    )
    token_id: Optional[str] = Field(None, description="Token ID for the operation")
    wallet_type: Optional[WalletType] = Field(WalletType.MPC, description="Wallet type")
    wallet_subtype: Optional[WalletSubtype] = Field(
        WalletSubtype.ORG_CONTROLLED, description="Wallet subtype"
    )

    operation_type: Optional[TokenizationTokenPermissionType] = Field(
        None, description="Operation type filter condition"
    )


class UserAddressListResponse(BaseModel):
    """User wallet address list response"""

    addresses: List[AppAddressData]


class TokenUpdateInfoRequest(BaseContractModel):
    pass


class TokenUpdateInfoResponse(BaseModel):
    """Query token update info response data"""

    signature_hash: str = Field(..., description="Signature hash")


class WalletBalanceRequest(BaseContractModel):
    """Wallet balance query request parameters"""

    wallet_address: str = Field(..., description="Wallet address")


class WalletBalanceResponse(BaseModel):
    """Wallet balance query response data"""

    wallet_address: str = Field(..., description="Wallet address")
    balance: str = Field(..., description="Wallet balance (formatted, e.g. 1234.56)")
    token: str = Field(..., description="Token symbol, e.g. USDT")


class CallData(BaseContractModel):
    function: Optional[str] = Field(..., description="Contract function name")
    params: Optional[Dict[str, Any]] = Field(
        ...,
        description="Contract function parameters",
    )


class ContractCallFeeRequest(BaseContractModel):
    source: Optional[TokenizationTokenOperationSource]
    data: Optional[List[CallData]] = Field(
        ..., description="Contract function call info list"
    )
    func_name: Optional[TokenOperationType] = Field(
        ..., description="Contract function name"
    )


class ContractCallRequest(BaseContractModel):
    source: Optional[TokenizationTokenOperationSource]
    data: Optional[List[CallData]] = Field(
        ..., description="Contract function call info list"
    )
    fee: Optional[TransactionRequestFee] = (Field(..., description="Contract fee"),)
    func_name: Optional[TokenOperationType] = Field(
        ..., description="Contract function name"
    )


class ContractReadRequest(BaseModel):
    chain_id: str = Field(..., description="The ID of the chain, e.g., 'ETH'.")
    contract_address: str = Field(
        ..., description="The address of the contract to be read."
    )
    func_name: str = Field(
        ..., description="The name of the view or pure function to call."
    )
    func_params: Optional[Dict[str, Any]] = Field(
        None,
        description="A dictionary of parameters to pass to the function, with parameter names as keys.",
    )


class ContractReadResponse(BaseModel):
    result: Any = Field(
        ..., description="The result returned by the contract function call."
    )
