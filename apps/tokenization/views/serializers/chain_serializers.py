from typing import List, Optional

from pydantic import BaseModel, Field


class ChainInfo(BaseModel):
    """链信息"""

    chain_id: str = Field(..., description="链ID")
    name: str = Field(..., description="链名称")
    icon: Optional[str] = Field(None, description="链图标")
    native_token: str = Field(..., description="原生代币符号")


class ChainListResponse(BaseModel):
    """链列表响应"""

    chains: List[ChainInfo] = Field(..., description="链列表")


class TokenInfoResponse(BaseModel):
    token_id: str = Field(..., description="token id")
    chain_id: str = Field(..., description="chain id")
    icon_url: str = Field(..., description="icon url")
    usd_rate: str = Field(..., description="usd rate")
