"""
Tokenization API Swagger配置
定义API的标签和描述信息
"""


class SwaggerTags:
    """API标签配置"""

    TOKEN = ("Token",)
    BALANCE = ("Balance",)
    TRANSFER = ("Transfer",)
    HISTORY = ("History",)
    TOKEN_OPERATIONS = ("Token Operations",)
    OPERATIONS = ("Operations",)

    # 新增标签
    TOKEN_MANAGEMENT = ("Token Management",)
    BLOCK_MANAGEMENT = ("Block Management",)
    PERMISSION_MANAGEMENT = ("Permission Management",)
    ADDRESS = ("Address",)
    CHAIN = "Chain Management"  # Update to English
    CONTRACT = ("Contract",)


class SwaggerDescription:
    """API描述配置"""

    # Token相关
    CREATE_TOKEN = "Deploy a new token contract to EVM network."
    DELETE_TOKEN = "Delete a failed token."

    GET_TOKEN_INFO = "Get token info"

    # Balance相关
    GET_BALANCE = "Query token balance for a specific wallet address."

    # Transfer相关
    TRANSFER_TOKEN = "Execute token transfer transaction."

    # History相关
    GET_TRANSACTION_HISTORY = "Query token transaction history for a specific address."

    # Token Operations相关
    TOKEN_OPERATION = "Execute various token operations including Deploy, Mint, Burn, Transfer, Pause, and Unpause with fee estimation."

    # 新增描述
    # Token Management相关
    UPDATE_TOKEN = "Update token information with contract ownership verification."
    MINT_TOKEN = "Mint new tokens to specified address."
    BURN_TOKEN = "Burn tokens from specified address."
    PAUSE_TOKEN = "Pause or unpause token operations."
    List_TOKEN = "List tokens to org"

    # Address Management相关
    GET_BLOCK_LIST = "Query allowlist or blocklist addresses with keyword filtering."
    DELETE_BLOCK = "Remove address from allowlist or blocklist."

    # Permission Management相关
    GET_PERMISSION_LIST = "Query permission list for addresses with filtering options."
    EDIT_PERMISSION = "Edit permissions for addresses. Supports adding and removing permissions for multiple addresses in a single request."
    DELETE_PERMISSION = "Remove all permissions for a specific address."

    # Restricted Access相关
    GET_RESTRICTED_ACCESS = "Query restricted access status for a specific token."
    UPDATE_RESTRICTED_ACCESS = "Update restricted access status for a specific token."

    LIST_CHAIN = """
    Get a list of supported blockchains in the system.
    Returns basic information for each chain, including:
    - Chain ID: The unique identifier of the blockchain
    - Name: The full name of the blockchain
    - Icon: The URL of the chain's icon image
    - Native Token: The symbol of the chain's native token
    """

    ESTIMATE_CONTRACT_FEE = "Estimate contract call fee"
    EXECUTE_CONTRACT_CALL = "Execute contract call"
