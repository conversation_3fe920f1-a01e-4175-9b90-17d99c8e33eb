import logging
import re

from cobo_libs.api.exceptions import BaseApiException
from cobo_libs.api.restful.base_view import BaseAPIView
from cobo_waas2.exceptions import ApiException

logger = logging.getLogger("bridge")


class TokenizationAPIView(BaseAPIView):
    def handle_exception(self, exc):
        if isinstance(exc, ApiException):
            api_exception = ApiException(exc)
            response_body = str(exc)
            # Extract error_code
            error_code_match = re.search(r"error_code=(\d+)", response_body)
            error_code = int(error_code_match.group(1)) if error_code_match else None

            # Extract error_message
            error_message_match = re.search(r"error_message='([^']*)'", response_body)
            error_message = (
                error_message_match.group(1) if error_message_match else None
            )

            # Extract status
            status_match = re.search(r"\((\d+)\)", str(api_exception.status))
            status = status_match.group(1) if status_match else None
            if status and error_code:
                exception = BaseApiException(
                    error_code=error_code,
                    error_message=error_message,
                    status_code=status,
                )
                response = super(TokenizationAPIView, self).handle_exception(
                    exc=exception
                )
            else:
                response = super(TokenizationAPIView, self).handle_exception(exc=exc)
        else:
            response = super(TokenizationAPIView, self).handle_exception(exc=exc)
        if response.status_code >= 500:
            logger.exception("Unknown Internal Error", exc_info=True)
        return response
