"""
合约相关的API视图
包含发币、转账、查询等功能
"""

import logging

from app_libs.auth.views.base import AuthAPIView
from app_libs.mfa.data.objects import APPRequestContext
from app_libs.mfa.decorators import cobo_extend_schema_with_mfa, mfa_guard_extend
from app_libs.utils.request import create_request_context
from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_waas2.models.transaction_request_fee import TransactionRequestFee

from apps.tokenization.controllers.token_controller import TokenController
from apps.tokenization.views.serializers.token_serializers import (
    BalanceRequest,
    BalanceResponse,
    CommonOperationResponse,
    ContractCallFeeRequest,
    ContractCallRequest,
    ContractReadRequest,
    ContractReadResponse,
    CreateTokenRequest,
    DeleteTokenRequest,
    ListTokenResponse,
    TokenInfoRequest,
    TransactionDetailResponse,
    TransactionDetailsRequest,
    TransactionHistoryRequest,
    TransactionHistoryResponse,
)

from ...enums import TokenizationFunction
from ...utils.permission_checker import permission_checker
from ..base import TokenizationAPIView
from ..controllers.contract_controller import ContractController
from .serializers.chain_serializers import TokenInfoResponse
from .swagger.view_swagger import SwaggerDescription, SwaggerTags

logger = logging.getLogger("apps.tokenization")


class TokenCreateView(TokenizationAPIView):
    """
    代币创建API
    用于部署新的合约
    """

    @cobo_extend_schema_with_mfa(
        summary="创建代币",
        tags=SwaggerTags.TOKEN,
        request=CreateTokenRequest,
        description=SwaggerDescription.CREATE_TOKEN,
        responses={200: CommonOperationResponse},
    )
    def post(self, request) -> CommonOperationResponse:
        """创建代币合约"""
        params: CreateTokenRequest = request.validated_data
        return self._do_create_token(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_create_token(
        cls, req: CreateTokenRequest, request_context: APPRequestContext
    ):
        return TokenController.create_token_api(request_context, req)


class TokenDeleteView(TokenizationAPIView):
    """
    代币创建API
    用于部署新的合约
    """

    @cobo_extend_schema_with_mfa(
        summary="删除代币",
        tags=SwaggerTags.TOKEN,
        request=DeleteTokenRequest,
        description=SwaggerDescription.DELETE_TOKEN,
        responses={200: CommonOperationResponse},
    )
    def post(self, request) -> CommonOperationResponse:
        """删除代币合约"""
        params: DeleteTokenRequest = request.validated_data
        return self._do_delete_token(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_delete_token(
        cls, req: DeleteTokenRequest, request_context: APPRequestContext
    ):
        return TokenController.delete_token_api(request_context.org_uuid, req)


class BalanceView(TokenizationAPIView):
    """
    代币余额查询API
    """

    @cobo_extend_schema(
        summary="查询代币余额",
        tags=SwaggerTags.BALANCE,
        parameter=BalanceRequest,
        description=SwaggerDescription.GET_BALANCE,
        responses={200: BalanceResponse},
    )
    def get(self, request) -> BalanceResponse:
        """查询代币余额"""
        params: BalanceRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_balance(org_id, params)


class TransactionHistoryView(TokenizationAPIView):
    """
    代币交易历史查询API
    """

    @cobo_extend_schema(
        summary="查询代币交易历史",
        tags=SwaggerTags.HISTORY,
        request=TransactionHistoryRequest,
        description=SwaggerDescription.GET_TRANSACTION_HISTORY,
        responses={200: TransactionHistoryResponse},
    )
    def post(self, request) -> TransactionHistoryResponse:
        """查询交易历史"""
        params: TransactionHistoryRequest = request.validated_data
        org_id = request.user.org_id

        logger.info(request.user)

        """跟前端的current对齐"""
        params.current = params.current - 1

        logger.info(f"list transaction history: {params}")
        return TokenController.get_transaction_history_api(org_id, params)


class TransactionDetailView(TokenizationAPIView):
    """
    单笔交易详情查询API
    """

    @cobo_extend_schema(
        summary="查询单笔交易详情",
        tags=SwaggerTags.HISTORY,
        parameter=TransactionDetailsRequest,
        description="根据交易ID查询单笔交易的详细信息。",
        responses={200: TransactionDetailResponse},
    )
    def get(self, request) -> TransactionDetailResponse:
        params: TransactionDetailsRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_transaction_details_api(org_id, params)


class OrgTokenListView(AuthAPIView):
    """
    组织代币列表查询API
    需要认证
    """

    @cobo_extend_schema(
        summary="查询组织的代币列表",
        tags=SwaggerTags.TOKEN,
        description=SwaggerDescription.List_TOKEN,
        responses={200: ListTokenResponse},
    )
    def get(self, request) -> ListTokenResponse:
        """查询组织的代币列表"""
        org_id = request.user.org_id
        return TokenController.get_token_list_api(org_id)


class TokenInfoView(AuthAPIView):
    """
    组织代币列表查询API
    需要认证
    """

    @cobo_extend_schema(
        summary="查询组织的代币列表",
        tags=SwaggerTags.TOKEN,
        description="Get Chain Info",
        responses={200: TokenInfoResponse},
        parameter=TokenInfoRequest,
    )
    def get(self, request) -> TokenInfoResponse:
        params: TokenInfoRequest = request.validated_data
        return TokenController.get_token_info(params)


class ContractCallView(TokenizationAPIView):
    """
    Contract Call API
    """

    @cobo_extend_schema_with_mfa(
        summary=SwaggerDescription.EXECUTE_CONTRACT_CALL,
        request=ContractCallRequest,
        responses={200: CommonOperationResponse},
        tags=SwaggerTags.CONTRACT,
    )
    @permission_checker(func_name=TokenizationFunction.MORE_ACTION_WRITE)
    def post(self, request):
        params: ContractCallRequest = request.validated_data
        return self._do_contract_call(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_contract_call(
        cls, req: ContractCallRequest, request_context: APPRequestContext
    ):
        return ContractController.call(
            request_context.org_uuid, req, request_context.user_email
        )


class ContractReadView(TokenizationAPIView):
    authentication_classes = []

    @cobo_extend_schema(
        tags=SwaggerTags.CONTRACT,
        summary="Dynamically call a read-only contract function",
        request=ContractReadRequest,
        responses={200: ContractReadResponse},
    )
    def post(self, request):
        params: ContractReadRequest = request.validated_data

        response_data = ContractController.read(params)
        return response_data


class ContractEstimateFeeView(TokenizationAPIView):
    @cobo_extend_schema(
        tags=SwaggerTags.CONTRACT,
        summary=SwaggerDescription.ESTIMATE_CONTRACT_FEE,
        request=ContractCallFeeRequest,
        responses={200: TransactionRequestFee},
    )
    def post(self, request):
        params: ContractCallFeeRequest = request.validated_data
        org_id = request.user.org_id
        return ContractController.estimate_fee(org_id, params).to_dict()
