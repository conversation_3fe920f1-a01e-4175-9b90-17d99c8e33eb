"""
代币管理相关的API视图
包含更新代币信息、Mint、Burn、黑白名单管理、权限管理、暂停恢复等功能
"""

import logging
import random

from app_libs.mfa.data.objects import APPRequestContext
from app_libs.mfa.decorators import cobo_extend_schema_with_mfa, mfa_guard_extend
from app_libs.utils.request import create_request_context
from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_waas2.models.transaction_request_fee import TransactionRequestFee

from apps.tokenization.controllers.token_controller import TokenController

from ...enums import TokenizationFunction
from ...utils.permission_checker import permission_checker
from ..base import TokenizationAPIView
from .serializers.token_serializers import (
    BlockCheckRequest,
    BlockCheckResponse,
    BlockListRequest,
    BlockListResponse,
    BlockUpdateRequest,
    BurnRequest,
    CommonOperationResponse,
    EstimateFeeRequest,
    MintRequest,
    PauseTokenRequest,
    PermissionListRequest,
    PermissionListResponse,
    RestrictedAccessRequest,
    RestrictedAccessResponse,
    UpdateRestrictedAccessRequest,
    UpdateTokenRequest,
    UserAddressListRequest,
    UserAddressListResponse,
    WalletBalanceRequest,
    WalletBalanceResponse,
)
from .swagger.view_swagger import SwaggerDescription, SwaggerTags

logger = logging.getLogger("apps.tokenization")


class UpdateTokenView(TokenizationAPIView):
    """
    更新代币信息API
    验证合约所有权
    """

    @cobo_extend_schema_with_mfa(
        summary="更新代币信息",
        tags=SwaggerTags.TOKEN_MANAGEMENT,
        request=UpdateTokenRequest,
        description=SwaggerDescription.UPDATE_TOKEN,
        responses={200: CommonOperationResponse},
    )
    def post(self, request) -> CommonOperationResponse:
        """更新代币信息（验证合约所有权）"""
        params: UpdateTokenRequest = request.validated_data
        return self._do_update_token(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_update_token(
        cls, req: UpdateTokenRequest, request_context: APPRequestContext
    ):
        return TokenController.update_token_api(request_context.org_uuid, req)


class MintTokenView(TokenizationAPIView):
    """
    铸币API
    """

    @cobo_extend_schema_with_mfa(
        summary="铸币",
        tags=SwaggerTags.TOKEN_MANAGEMENT,
        request=MintRequest,
        description=SwaggerDescription.MINT_TOKEN,
        responses={200: CommonOperationResponse},
    )
    @permission_checker(func_name=TokenizationFunction.MINT)
    def post(self, request) -> CommonOperationResponse:
        """铸币操作"""
        params: MintRequest = request.validated_data
        return self._do_mint_token(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_mint_token(cls, req: MintRequest, request_context: APPRequestContext):
        req.tx_detail.app_initiator = request_context.user_email
        return TokenController.mint_token_api(request_context.org_uuid, req)


class BurnTokenView(TokenizationAPIView):
    """
    销毁代币API
    """

    @cobo_extend_schema_with_mfa(
        summary="销毁代币",
        tags=SwaggerTags.TOKEN_MANAGEMENT,
        request=BurnRequest,
        description=SwaggerDescription.BURN_TOKEN,
        responses={200: CommonOperationResponse},
    )
    @permission_checker(func_name=TokenizationFunction.BURN)
    def post(self, request) -> CommonOperationResponse:
        """销毁代币操作"""
        params: BurnRequest = request.validated_data
        return self._do_burn_token(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_burn_token(cls, req: BurnRequest, request_context: APPRequestContext):
        req.tx_detail.app_initiator = request_context.user_email
        return TokenController.burn_token_api(request_context.org_uuid, req)


class BlockListView(TokenizationAPIView):
    """
    查询黑白名单地址列表API
    """

    @cobo_extend_schema(
        summary="查询黑白名单地址列表",
        tags=SwaggerTags.BLOCK_MANAGEMENT,
        parameter=BlockListRequest,
        description=SwaggerDescription.GET_BLOCK_LIST,
        responses={200: BlockListResponse},
    )
    def get(self, request) -> BlockListResponse:
        """查询黑白名单地址列表"""
        params: BlockListRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_block_list_api(org_id, params)


class PermissionListView(TokenizationAPIView):
    """
    查询权限列表API
    """

    @cobo_extend_schema(
        summary="查询权限列表",
        tags=SwaggerTags.PERMISSION_MANAGEMENT,
        parameter=PermissionListRequest,
        description=SwaggerDescription.GET_PERMISSION_LIST,
        responses={200: PermissionListResponse},
    )
    def get(self, request) -> PermissionListResponse:
        """查询权限列表"""
        params: PermissionListRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_permission_list_api(org_id, params)


class PermissionEditView(TokenizationAPIView):
    pass


class PauseTokenView(TokenizationAPIView):
    """
    暂停/恢复代币API
    """

    @cobo_extend_schema_with_mfa(
        summary="暂停/恢复代币",
        tags=SwaggerTags.TOKEN_MANAGEMENT,
        request=PauseTokenRequest,
        description=SwaggerDescription.PAUSE_TOKEN,
        responses={200: CommonOperationResponse},
    )
    @permission_checker(func_name=TokenizationFunction.PAUSED)
    def post(self, request) -> CommonOperationResponse:
        """暂停/恢复代币操作"""
        params: PauseTokenRequest = request.validated_data
        return self._do_pause_token(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_pause_token(
        cls, req: PauseTokenRequest, request_context: APPRequestContext
    ):
        req.tx_detail.app_initiator = request_context.user_email
        return TokenController.pause_token_api(request_context.org_uuid, req)


class RestrictedAccessView(TokenizationAPIView):
    """
    受限访问状态管理API
    GET: 查询受限访问状态（不需要认证）
    POST: 修改受限访问状态（需要认证）
    """

    @cobo_extend_schema(
        summary="查询受限访问状态",
        tags=SwaggerTags.TOKEN_MANAGEMENT,
        parameter=RestrictedAccessRequest,
        description=SwaggerDescription.GET_RESTRICTED_ACCESS,
        responses={200: RestrictedAccessResponse},
    )
    def get(self, request) -> RestrictedAccessResponse:
        """查询代币的受限访问状态"""
        params: RestrictedAccessRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_restricted_access_api(org_id, params)

    @cobo_extend_schema_with_mfa(
        summary="修改受限访问状态",
        tags=SwaggerTags.TOKEN_MANAGEMENT,
        request=UpdateRestrictedAccessRequest,
        description=SwaggerDescription.UPDATE_RESTRICTED_ACCESS,
        responses={200: CommonOperationResponse},
    )
    @permission_checker(func_name=TokenizationFunction.RESTRICTED)
    def post(self, request) -> CommonOperationResponse:
        """修改代币的受限访问状态"""
        params: UpdateRestrictedAccessRequest = request.validated_data
        return self._do_update_restricted_access(
            params, create_request_context(request)
        )

    @classmethod
    @mfa_guard_extend()
    def _do_update_restricted_access(
        cls, req: UpdateRestrictedAccessRequest, request_context: APPRequestContext
    ):
        req.tx_detail.app_initiator = request_context.user_email
        return TokenController.update_restricted_access_api(
            request_context.org_uuid, req
        )


class BlockUpdateView(TokenizationAPIView):
    """
    添加黑白名单地址API
    """

    @cobo_extend_schema_with_mfa(
        summary="添加黑白名单地址",
        tags=SwaggerTags.BLOCK_MANAGEMENT,
        request=BlockUpdateRequest,
        description="Add Block to allowlist or blocklist.",
        responses={200: CommonOperationResponse},
    )
    @permission_checker(func_name=TokenizationFunction.BLOCKLIST)
    def post(self, request) -> CommonOperationResponse:
        """添加黑白名单地址操作"""
        params: BlockUpdateRequest = request.validated_data
        return self._do_update_block(params, create_request_context(request))

    @classmethod
    @mfa_guard_extend()
    def _do_update_block(
        cls, req: BlockUpdateRequest, request_context: APPRequestContext
    ):
        return TokenController.update_block_api(
            request_context.org_uuid, req, request_context.user_email
        )


class BlockCheckView(TokenizationAPIView):
    """
    地址操作权限校验API
    """

    @cobo_extend_schema(
        summary="校验地址是否允许操作",
        tags=SwaggerTags.BLOCK_MANAGEMENT,
        request=BlockCheckRequest,
        responses={200: BlockCheckResponse},
        description="校验某个地址是否允许进行操作，返回true/false。",
    )
    def post(self, request) -> BlockCheckResponse:
        params: BlockCheckRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.check_block_api(org_id, params)


class EstimateOperationFeeView(TokenizationAPIView):
    """
    代币操作预估手续费API
    """

    @cobo_extend_schema(
        summary="查询代币操作预估手续费",
        tags=SwaggerTags.OPERATIONS,
        request=EstimateFeeRequest,
        responses=TransactionRequestFee,
        description="根据操作类型和参数，返回预估手续费信息。",
    )
    def post(self, request) -> TransactionRequestFee:
        params: EstimateFeeRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.estimate_operation_fee_api(org_id, params).to_dict()


class UserAddressListView(TokenizationAPIView):
    """
    用户钱包地址列表API，支持按operationType过滤
    """

    @cobo_extend_schema(
        summary="获取用户钱包所有地址（支持按操作类型过滤）",
        tags=SwaggerTags.ADDRESS,
        parameter=UserAddressListRequest,
        responses={200: UserAddressListResponse},
        description="获取用户钱包的所有地址，并可根据operationType对地址进行权限过滤。",
    )
    def get(self, request) -> UserAddressListResponse:
        params: UserAddressListRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_user_address_list_api(org_id, params)


class WalletBalanceView(TokenizationAPIView):
    """
    钱包余额查询API
    """

    @cobo_extend_schema(
        summary="查询钱包余额",
        tags=SwaggerTags.BALANCE,
        parameter=WalletBalanceRequest,
        responses={200: WalletBalanceResponse},
        description="根据钱包地址查询余额。",
    )
    def get(self, request) -> WalletBalanceResponse:
        params: WalletBalanceRequest = request.validated_data
        org_id = request.user.org_id
        return TokenController.get_wallet_balance_api(org_id, params)


def generate_mock_tx_id():
    return "0x" + "".join(random.choices("0123456789abcdef", k=64))
