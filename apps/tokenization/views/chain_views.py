from cobo_libs.api.decorator.drf import cobo_extend_schema
from cobo_libs.api.restful.base_view import BaseAPIView

from apps.tokenization.controllers.chain_controller import ChainController
from apps.tokenization.views.serializers.chain_serializers import ChainListResponse
from apps.tokenization.views.swagger.view_swagger import SwaggerDescription, SwaggerTags


class ChainListView(BaseAPIView):
    """链列表API"""

    @cobo_extend_schema(
        summary="获取支持的链列表",
        tags=[SwaggerTags.CHAIN],
        description=SwaggerDescription.LIST_CHAIN,
        responses={200: ChainListResponse},
    )
    def get(self, request) -> ChainListResponse:
        """获取支持的链列表"""
        org_id = request.user.org_id
        return ChainController.get_chain_list(org_id)
