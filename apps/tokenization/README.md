# ERC-20代币化API文档

这个模块提供了基于EVM网络的ERC-20代币相关API接口，支持代币创建、查询、转账等功能。

## API列表

### 1. 创建ERC-20代币 
**POST** `/v1/erc20/token/create/`

创建并部署新的ERC-20代币合约。

**请求参数:**
```json
{
    "name": "My Token",
    "symbol": "MTK", 
    "decimals": 18,
    "total_supply": "1000000000000000000000000",
    "owner_address": "******************************************",
    "network": "ethereum"
}
```

**响应示例:**
```json
{
    "success": true,
    "result": {
        "contract_address": "******************************************",
        "transaction_hash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
        "token_info": {
            "name": "My Token",
            "symbol": "MTK",
            "decimals": 18,
            "total_supply": "1000000000000000000000000"
        }
    }
}
```

### 2. 查询代币信息
**GET** `/v1/erc20/token/info/`

根据合约地址查询代币基本信息。

**查询参数:**
- `contract_address`: 合约地址
- `network`: 网络类型 (ethereum, bsc, polygon, arbitrum)

**响应示例:**
```json
{
    "success": true,
    "result": {
        "name": "My Token",
        "symbol": "MTK",
        "decimals": 18,
        "total_supply": "1000000000000000000000000",
        "contract_address": "******************************************",
        "network": "ethereum"
    }
}
```

### 3. 查询代币余额
**GET** `/v1/erc20/balance/`

查询指定钱包地址的代币余额。

**查询参数:**
- `contract_address`: 合约地址
- `wallet_address`: 钱包地址
- `network`: 网络类型

**响应示例:**
```json
{
    "success": true,
    "result": {
        "balance": "500000000000000000000",
        "formatted_balance": "500.0",
        "contract_address": "******************************************",
        "wallet_address": "******************************************",
        "network": "ethereum"
    }
}
```

### 4. 代币转账
**POST** `/v1/erc20/transfer/`

执行ERC-20代币转账交易。

**请求参数:**
```json
{
    "contract_address": "******************************************",
    "from_address": "******************************************",
    "to_address": "0x8ba1f109551bD432803012645Hac136c9223Ba17",
    "amount": "1000000000000000000",
    "network": "ethereum",
    "private_key": "0xprivate_key_here"
}
```

**⚠️ 注意:** 私钥参数仅用于开发测试，生产环境需要使用更安全的签名方式。

**响应示例:**
```json
{
    "success": true,
    "result": {
        "transaction_hash": "0xdef456789012345678901234567890123456789012345678901234567890abcd",
        "from_address": "******************************************",
        "to_address": "0x8ba1f109551bD432803012645Hac136c9223Ba17",
        "amount": "1000000000000000000",
        "contract_address": "******************************************",
        "network": "ethereum",
        "status": "pending"
    }
}
```

### 5. 查询交易历史
**GET** `/v1/erc20/transactions/`

查询指定地址的代币交易记录。

**查询参数:**
- `contract_address`: 合约地址
- `address`: 查询地址
- `network`: 网络类型
- `page`: 页码 (可选，默认1)
- `page_size`: 每页条数 (可选，默认20)

**响应示例:**
```json
{
    "success": true,
    "result": {
        "transactions": [
            {
                "hash": "0xabc123456789",
                "from": "******************************************",
                "to": "******************************************",
                "amount": "1000000000000000000",
                "formatted_amount": "1.0",
                "timestamp": 1703123456,
                "block_number": 18900000,
                "status": "success"
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total_count": 50,
            "total_pages": 3
        }
    }
}
```

## 支持的网络

- `ethereum`: 以太坊主网
- `bsc`: 币安智能链
- `polygon`: Polygon网络  
- `arbitrum`: Arbitrum网络

## 认证要求

- **需要认证的API:**
  - 创建代币 (`/v1/erc20/token/create/`)
  - 代币转账 (`/v1/erc20/transfer/`)

- **无需认证的API:**
  - 查询代币信息 (`/v1/erc20/token/info/`)
  - 查询余额 (`/v1/erc20/balance/`)
  - 查询交易历史 (`/v1/erc20/transactions/`)

## 错误响应格式

所有API的错误响应都遵循统一格式:

```json
{
    "success": false,
    "error": "错误描述信息"
}
```

## 开发说明

目前所有API都返回模拟数据，实际使用时需要：

1. 实现真实的区块链服务集成
2. 添加适当的参数验证
3. 实现安全的私钥管理机制
4. 添加交易状态跟踪功能
5. 实现错误重试机制

## 使用示例

### 使用curl测试API

```bash
# 查询代币信息
curl -X GET "http://localhost:8000/v1/erc20/token/info/?contract_address=******************************************&network=ethereum"

# 查询余额
curl -X GET "http://localhost:8000/v1/erc20/balance/?contract_address=******************************************&wallet_address=******************************************&network=ethereum"

# 创建代币 (需要认证)
curl -X POST "http://localhost:8000/v1/erc20/token/create/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Test Token",
    "symbol": "TEST",
    "total_supply": "1000000000000000000000000",
    "owner_address": "******************************************",
    "network": "ethereum"
  }'
```

### 前端JavaScript示例

```javascript
// 查询代币信息
async function getTokenInfo(contractAddress, network) {
    const response = await fetch(`/v1/erc20/token/info/?contract_address=${contractAddress}&network=${network}`);
    const data = await response.json();
    return data;
}

// 创建代币
async function createToken(tokenData, authToken) {
    const response = await fetch('/v1/erc20/token/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(tokenData)
    });
    const data = await response.json();
    return data;
}
``` 