from typing import List

import cobo_waas2

from apps.clients.waas_dev_client import WaaSDevApiClientManager
from apps.tokenization.utils import paginated_request
from apps.tokenization.views.serializers.chain_serializers import (
    ChainInfo,
    ChainListResponse,
)
from apps.utils.token import TokenManager


class ChainController:
    """链控制器"""

    @classmethod
    def get_chain_list(cls, org_id: str) -> ChainListResponse:
        """获取支持的链列表"""
        chain_infos: List[ChainInfo] = []

        # 遍历支持的链
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            enabled_chains = paginated_request(api.list_tokenization_supported_chains)
            for chain_id in enabled_chains:
                # 从TokenManager获取链信息
                chain_info = TokenManager.get_token_info(chain_id)
                if not chain_info:
                    continue

                chain_infos.append(
                    ChainInfo(
                        chain_id=chain_id,
                        name=chain_info.name,
                        icon=chain_info.icon_url,
                        native_token=chain_info.token_id,
                    )
                )

        return ChainListResponse(chains=chain_infos)
