import re
from typing import Any

import cobo_waas2
from cobo_waas2 import (
    TokenizationContractCallEstimateFeeParams,
    TokenizationContractCallParamsData,
    TokenizationContractCallRequest,
    TokenizationEstimateFeeRequest,
    TokenizationEstimateFeeRequestOperationParams,
    TokenizationEvmContractCallParams,
    TokenizationOperationType,
    TransactionRequestFee,
)
from pydantic import BaseModel
from web3 import Web3

from apps.clients.waas_dev_client import WaaSDevApiClientManager
from apps.tokenization.views.serializers.token_serializers import (
    CommonOperationResponse,
    ContractCallFeeRequest,
    ContractCallRequest,
    ContractReadRequest,
    ContractReadResponse,
)
from apps.utils.CoboERC20 import COBO_ERC20_ABI
from apps.utils.web3 import encode_function_input, web3_contract, web3_instance


class ContractController:
    @classmethod
    def estimate_fee(
        cls, org_id: str, req: ContractCallFeeRequest
    ) -> TransactionRequestFee:
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)

            if req.func_name == "multicall":
                call_data = encode_function_input(
                    COBO_ERC20_ABI, req.func_name, BaseModel.model_dump(req)
                )
            else:
                call_data = encode_function_input(
                    COBO_ERC20_ABI, req.func_name, req.data[0].params
                )

            param_call = TokenizationEvmContractCallParams(
                type="EVM_Contract",
                calldata=call_data,
            )

            param: TokenizationContractCallEstimateFeeParams = (
                TokenizationContractCallEstimateFeeParams(
                    source=req.source,
                    data=TokenizationContractCallParamsData.from_dict(
                        param_call.to_dict()
                    ),
                    token_id=req.token_id,
                    operation_type=TokenizationOperationType.CONTRACTCALL,
                )
            )

            resp = api.estimate_tokenization_fee(
                tokenization_estimate_fee_request=TokenizationEstimateFeeRequest(
                    operation_params=TokenizationEstimateFeeRequestOperationParams.from_dict(
                        param.to_dict()
                    )
                )
            )

            return TransactionRequestFee.from_dict(
                {
                    "fee_type": resp.actual_instance.fee_type,
                    "token_id": resp.actual_instance.token_id,
                    **resp.actual_instance.fast.to_dict(),
                }
            )

    @classmethod
    def call(
        cls, org_id: str, req: ContractCallRequest, user_email: str
    ) -> CommonOperationResponse:
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)

            if req.func_name == "multicall":
                call_data = encode_function_input(
                    COBO_ERC20_ABI, req.func_name, BaseModel.model_dump(req)
                )
            else:
                call_data = encode_function_input(
                    COBO_ERC20_ABI, req.func_name, req.data[0].params
                )

            param_data = TokenizationEvmContractCallParams(
                type="EVM_Contract",
                calldata=call_data,
            )

            sdk_request = TokenizationContractCallRequest(
                source=req.source,
                data=TokenizationContractCallParamsData.from_dict(param_data.to_dict()),
                fee=req.fee,
                app_initiator=user_email,
            )

            resp = api.tokenization_contract_call(
                token_id=req.token_id, tokenization_contract_call_request=sdk_request
            )

            return CommonOperationResponse(
                id=resp.activity_id,
            )

    @classmethod
    def read(cls, req: ContractReadRequest) -> ContractReadResponse:
        """
        Dynamically calls a read-only (view or pure) function on a contract
        using direct ABI + function name + params approach.
        """
        # 1. 验证函数是否在ABI中，且为只读
        func_abi = next(
            (
                item
                for item in COBO_ERC20_ABI
                if item.get("type") == "function" and item.get("name") == req.func_name
            ),
            None,
        )
        if not func_abi:
            raise ValueError(f"Function '{req.func_name}' not found in ABI.")
        if func_abi.get("stateMutability") not in ["view", "pure"]:
            raise ValueError(f"Function '{req.func_name}' is not a read-only function.")

        # 2. 创建合约实例
        try:
            contract = web3_contract(
                chain_id=req.chain_id,
                contract_address=req.contract_address,
                abi=COBO_ERC20_ABI,
            )
        except Exception as e:
            raise ConnectionError(
                f"Failed to connect to contract at {req.contract_address} on chain {req.chain_id}: {e}"
            )

        # 3. 按ABI顺序准备和转换参数
        params_dict = req.func_params if req.func_params is not None else {}
        ordered_params = []
        param_name = None
        try:
            for param_abi in func_abi.get("inputs", []):
                param_name = param_abi["name"]
                param_type = param_abi["type"]
                raw_value = params_dict[param_name]

                converted_value = raw_value
                if (
                    param_type.startswith("bytes")
                    and isinstance(raw_value, str)
                    and not raw_value.startswith("0x")
                ):
                    # Use web3 instance for keccak hashing
                    web3 = web3_instance(req.chain_id)
                    converted_value = web3.keccak(text=raw_value)
                    match = re.match(r"bytes(\d+)", param_type)
                    if match:
                        num_bytes = int(match.group(1))
                        converted_value = converted_value[:num_bytes]
                elif (
                    param_type.startswith("uint") or param_type.startswith("int")
                ) and isinstance(raw_value, str):
                    converted_value = int(raw_value)
                elif param_type == "address" and isinstance(raw_value, str):
                    # Ensure address is checksummed
                    converted_value = Web3.to_checksum_address(raw_value)

                ordered_params.append(converted_value)
        except KeyError:
            raise ValueError(
                f"Missing parameter '{param_name}' for function '{req.func_name}'."
            )

        # 4. 直接调用合约函数
        try:
            contract_function = getattr(contract.functions, req.func_name)
            result = contract_function(*ordered_params).call()
        except AttributeError:
            raise ValueError(f"Function '{req.func_name}' not found on contract.")
        except Exception as e:
            raise ConnectionError(
                f"Contract call failed for function '{req.func_name}': {e}"
            )

        # 5. 处理返回结果并封装到响应模型中
        serialized_result: Any
        if isinstance(result, bytes):
            serialized_result = "0x" + result.hex()
        else:
            serialized_result = result

        return ContractReadResponse(result=serialized_result)
