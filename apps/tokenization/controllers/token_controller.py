import logging
from typing import Any, Dict, List

import cobo_waas2
import web3
from app_libs.mfa.data.objects import APPRequestContext
from cobo_app_python_api.api.wallet_api import WalletApi
from cobo_app_python_api.models.app_address_data import AppAddressData
from cobo_waas2 import (
    TokenizationContractCallParamsData,
    TokenizationContractCallRequest,
    TokenizationEstimateFeeRequest,
    TokenizationEvmContractCallParams,
    TokenizationIssueTokenParamsTokenParams,
    TokenizationOperationResponse,
    TokenizationTokenPermissionParams,
    TransactionRequestFee,
    TokenizationSolTokenPermissionParams,
    TokenizationTokenStandard,
    TokenizationSOLTokenParams,
)
from cobo_waas2.models.tokenization_erc20_token_params import (
    TokenizationERC20TokenParams,
)
from cobo_waas2.models.tokenization_issued_token_request import (
    TokenizationIssuedTokenRequest,
)
from cobo_waas2.models.tokenization_token_operation_source import (
    TokenizationTokenOperationSource,
)
from django.conf import settings
from web3 import Web3

from apps.clients.cobo_app_client import CoboAppApiClientManager
from apps.clients.waas_dev_client import WaaSDevApiClientManager
from apps.tokenization.common.block_type import BlockType
from apps.tokenization.common.pause_type import PauseType
from apps.tokenization.controllers.wallet_controller import WalletController
from apps.tokenization.param_processor import (
    extract_amount_from_function,
    get_selector_from_calldata,
    process_params_by_selector,
)
from apps.tokenization.utils import decimal_to_string, paginated_request
from apps.tokenization.views.serializers.chain_serializers import TokenInfoResponse
from apps.tokenization.views.serializers.token_serializers import (
    BalanceRequest,
    BalanceResponse,
    BlockCheckRequest,
    BlockCheckResponse,
    BlockListRequest,
    BlockListResponse,
    BlockUpdateRequest,
    BurnRequest,
    CommonOperationResponse,
    CreateTokenRequest,
    DeleteTokenRequest,
    EstimateFeeRequest,
    FunctionsData,
    ListTokenResponse,
    MintRequest,
    PaginationInfo,
    PauseTokenRequest,
    PermissionEditRequest,
    PermissionListRequest,
    PermissionListResponse,
    RestrictedAccessRequest,
    RestrictedAccessResponse,
    TokenInfo,
    TokenInfoRequest,
    TokenizationBlocklistAddressNote,
    Transaction,
    TransactionDetailResponse,
    TransactionDetailsRequest,
    TransactionHistoryRequest,
    TransactionHistoryResponse,
    UpdateRestrictedAccessRequest,
    UpdateTokenRequest,
    UserAddressListRequest,
    UserAddressListResponse,
    WalletBalanceRequest,
    WalletBalanceResponse,
)
from apps.utils.selector import get_name_by_selector, smart_decode_function_input
from apps.utils.token import TokenManager

logger = logging.getLogger("tokenization")

# ERC20 权限角色映射
ERC20_ROLE_MAPPING = {
    "Admin": "DEFAULT_ADMIN_ROLE",
    "Mint": "MINTER_ROLE",
    "Burn": "BURNER_ROLE",
    "Pause": "PAUSER_ROLE",
    "Manage": "MANAGER_ROLE",
    "Salvage": "SALVAGER_ROLE",
    "Upgrade": "UPGRADER_ROLE",
}

# 特殊角色映射 - 这些角色需要特殊处理
ROLE_MAPPING = {
    "DEFAULT_ADMIN_ROLE": "0x0000000000000000000000000000000000000000000000000000000000000000",
    # 其他角色使用keccak256哈希
}

# Token2022 权限类型映射
TOKEN2022_AUTHORITY_MAPPING = {
    "Mint": "mintAuthority",
    "Freeze": "freezeAuthority",
    "Close": "closeAuthority",
}


class TokenController:
    @classmethod
    def get_balance(cls, org_id: str, req: BalanceRequest) -> BalanceResponse:
        """查询代币余额"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            data = paginated_request(
                cobo_waas2.TokenizationApi(client).list_tokenization_holdings,
                token_id=req.token_id,
            )
            return BalanceResponse(
                balance=data,
            )

    @classmethod
    def create_token_api(
        cls, ctx: APPRequestContext, req: CreateTokenRequest
    ) -> CommonOperationResponse:
        """创建代币（标准API风格）"""

        with WaaSDevApiClientManager.get_client(ctx.org_uuid) as client:
            # 1. 构造 source
            source = req.source
            source_address = web3.Web3.to_checksum_address(
                req.source.actual_instance.address
            )
            source.actual_instance.address = source_address

            # 2. 构造 token_params
            if req.standard == TokenizationTokenStandard.ERC20:
                permissions: TokenizationTokenPermissionParams = (
                    TokenizationTokenPermissionParams(
                        admin=[req.admin] if req.admin else [],
                        minter=[req.minter] if req.minter else [],
                        manager=[req.manager] if req.manager else [],
                    )
                )
                token_params = TokenizationIssueTokenParamsTokenParams(
                    actual_instance=TokenizationERC20TokenParams(
                        name=req.name,
                        symbol=req.symbol,
                        decimals=req.decimals,
                        standard=req.standard,
                        token_access_activated=req.allowlist_activated,
                        permissions=permissions,
                    )
                )
            elif req.standard == TokenizationTokenStandard.SPLTOKEN2022:
                permissions: TokenizationSolTokenPermissionParams = (
                    TokenizationSolTokenPermissionParams(
                        permanent_delegate=[req.permanent_delegate]
                        if req.permanent_delegate
                        else [],
                        minter=[req.minter] if req.minter else [],
                        pauser=[req.pauser] if req.pauser else [],
                        freezer=[req.freezer] if req.freezer else [],
                        updater=[req.updater] if req.updater else [],
                    )
                )
                token_params = TokenizationIssueTokenParamsTokenParams(
                    actual_instance=TokenizationSOLTokenParams(
                        standard=req.standard,
                        name=req.name,
                        symbol=req.symbol,
                        decimals=req.decimals,
                        token_access_activated=req.allowlist_activated,
                        permissions=permissions,
                    )
                )
            
            # 3. 构造 IssueTokenRequest
            issue_token_request = TokenizationIssuedTokenRequest(
                chain_id=req.chain,
                source=source,
                token_params=token_params,
                app_initiator=ctx.user_email,
                fee=req.est_fee,
            )
            resp = cobo_waas2.TokenizationApi(client).issue_token(issue_token_request)
            return CommonOperationResponse(
                id=getattr(resp, "activity_id", "mock_activity_id")
            )

    @classmethod
    def update_token_api(
        cls, org_id: str, req: UpdateTokenRequest
    ) -> CommonOperationResponse:
        """更新代币信息"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.tokenization_contract_call(
                token_id=req.token_id,
                source=TokenizationTokenOperationSource(
                    type=req.tx_detail.source, fee=req.est_fee
                ),
                # TODO: 根据req.tx_detail构造合约调用参数
            )
            return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def mint_token_api(cls, org_id: str, req: MintRequest) -> CommonOperationResponse:
        """铸币"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.mint_tokenization(
                token_id=req.token_id,
                tokenization_mint_token_request=req.tx_detail,
            )
            return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def burn_token_api(cls, org_id: str, req: BurnRequest) -> CommonOperationResponse:
        """销毁代币"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.burn_tokenization(
                token_id=req.token_id, tokenization_burn_token_request=req.tx_detail
            )
            return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def pause_token_api(
        cls, org_id: str, req: PauseTokenRequest
    ) -> CommonOperationResponse:
        """暂停/恢复代币"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            if req.type == PauseType.PAUSE:
                resp = api.pause_tokenization(
                    token_id=req.token_id,
                    tokenization_pause_token_request=cobo_waas2.TokenizationPauseTokenRequest.from_dict(
                        req.tx_detail.to_dict()
                    ),
                )
            else:
                resp = api.unpause_tokenization(
                    token_id=req.token_id,
                    tokenization_unpause_token_request=cobo_waas2.TokenizationUnpauseTokenRequest.from_dict(
                        req.tx_detail.to_dict()
                    ),
                )
            return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def get_block_list_api(
        cls, org_id: str, req: BlockListRequest
    ) -> BlockListResponse:
        """查询黑白名单地址列表"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)

            token_detail_info = api.get_tokenization_info(token_id=req.token_id)

            if req.type == BlockType.ACCESS:
                resp = paginated_request(
                    api.list_tokenization_allowlist_addresses, token_id=req.token_id
                )
            else:
                resp = paginated_request(
                    api.list_tokenization_blocklist_addresses, token_id=req.token_id
                )

            data = [
                TokenizationBlocklistAddressNote.model_validate(item.to_dict())
                for item in resp
            ]

            if (
                token_detail_info.token_access_activated
                and req.type == BlockType.ACCESS
            ):
                block_list = paginated_request(
                    api.list_tokenization_blocklist_addresses, token_id=req.token_id
                )
                block_addresses = [item.address for item in block_list]
                for item in data:
                    if item.address in block_addresses:
                        item.is_blocked = False
                    else:
                        item.is_blocked = True

            return BlockListResponse(pagination=None, data=data)

    @classmethod
    def update_block_api(
        cls, org_id: str, req: BlockUpdateRequest, user_email: str
    ) -> CommonOperationResponse:
        """添加黑白名单地址"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            if req.type == BlockType.ACCESS:
                req.allow.app_initiator = user_email
                resp = api.update_allowlist_addresses(
                    token_id=req.token_id,
                    tokenization_update_allowlist_addresses_request=req.allow,
                )
            else:
                req.block.app_initiator = user_email
                resp = api.update_tokenization_blocklist_addresses(
                    token_id=req.token_id,
                    tokenization_update_blocklist_addresses_request=req.block,
                )
            return CommonOperationResponse(
                id=resp.activity_id,
            )

    @classmethod
    def check_block_api(cls, org_id: str, req: BlockCheckRequest) -> BlockCheckResponse:
        """检查地址是否在黑白名单中"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            # 先检查白名单
            allow_resp = api.list_tokenization_allowlist_addresses(
                token_id=req.token_id, keyword=req.address
            )
            # 再检查黑名单
            block_resp = api.list_tokenization_blocklist_addresses(
                token_id=req.token_id, keyword=req.address
            )
            # 如果地址在白名单中且不在黑名单中，则允许操作
            allowed = any(
                x.address == req.address for x in allow_resp.data
            ) and not any(x.address == req.address for x in block_resp.data)
            return BlockCheckResponse(allowed=allowed)

    @classmethod
    def get_permission_list_api(
        cls, org_id: str, req: PermissionListRequest
    ) -> PermissionListResponse:
        """查询权限列表"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.get_tokenization_info(token_id=req.token_id)

            return PermissionListResponse(data=resp.permissions)

    @classmethod
    def edit_permission_api(
        cls, org_id: str, req: PermissionEditRequest, user_email: str
    ) -> CommonOperationResponse:
        """编辑权限"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)

            # 获取token信息以确定类型
            token_info = api.get_tokenization_info(token_id=req.token_id)
            token_standard = token_info.standard

            if token_standard == "ERC20":
                return cls._edit_erc20_permissions(api, req, user_email)
            elif token_standard == "SPL_TOKEN_2022":
                return cls._edit_token2022_permissions(api, req, user_email)
            else:
                raise ValueError(f"Unsupported token standard: {token_standard}")

    @classmethod
    def _edit_erc20_permissions(
        cls, api: cobo_waas2.TokenizationApi, req: PermissionEditRequest, user_email: str
    ) -> CommonOperationResponse:
        """编辑ERC20权限"""
        from apps.utils.CoboERC20 import COBO_ERC20_ABI
        from apps.utils.web3 import encode_function_input

        # 构造multicall数据
        call_data_list = []

        for operation in req.tx_detail.operations:
            for permission in operation.permissions:
                # 获取角色名称
                role_name = ERC20_ROLE_MAPPING.get(permission)
                if not role_name:
                    raise ValueError(f"Unknown permission: {permission}")

                # 构造函数调用
                if operation.operation == "add":
                    func_name = "grantRole"
                elif operation.operation == "remove":
                    func_name = "revokeRole"
                else:
                    raise ValueError(f"Unknown operation: {operation.operation}")

                # 获取角色哈希值
                if role_name in ROLE_MAPPING:
                    role_hash = ROLE_MAPPING[role_name]
                else:
                    # 使用keccak256计算角色哈希
                    role_hash = web3.Web3.keccak(text=role_name).hex()

                # 构造参数
                params = {
                    "role": role_hash,
                    "account": operation.address
                }

                # 编码函数调用
                call_data = encode_function_input(COBO_ERC20_ABI, func_name, params)
                call_data_list.append(call_data)

        # 如果只有一个调用，直接使用该调用；否则使用multicall
        if len(call_data_list) == 1:
            final_call_data = call_data_list[0]
        else:
            # 使用multicall
            multicall_params = {"data": call_data_list}
            final_call_data = encode_function_input(COBO_ERC20_ABI, "multicall", multicall_params)

        # 构造合约调用参数
        param_data = TokenizationEvmContractCallParams(
            type="EVM_Contract",
            calldata=final_call_data,
        )

        sdk_request = TokenizationContractCallRequest(
            source=req.tx_detail.source,
            data=TokenizationContractCallParamsData.from_dict(param_data.to_dict()),
            fee=req.est_fee,
            app_initiator=user_email,
        )

        resp = api.tokenization_contract_call(
            token_id=req.token_id, tokenization_contract_call_request=sdk_request
        )

        return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def _edit_token2022_permissions(
        cls, api: cobo_waas2.TokenizationApi, req: PermissionEditRequest, user_email: str
    ) -> CommonOperationResponse:
        """编辑Token2022权限"""
        try:
            import anchorpy
            from apps.solana.idls.Token2022 import TOKEN_2022_IDL
        except ImportError:
            raise ImportError("anchorpy library is required for Token2022 operations")

        # 构造指令列表
        instructions = []

        for operation in req.tx_detail.operations:
            for permission in operation.permissions:
                # 获取权限类型
                authority_type = TOKEN2022_AUTHORITY_MAPPING.get(permission)
                if not authority_type:
                    raise ValueError(f"Unknown permission for Token2022: {permission}")

                # 构造setAuthority指令
                if operation.operation == "add":
                    # 添加权限：设置新的authority
                    new_authority = operation.address
                elif operation.operation == "remove":
                    # 移除权限：设置authority为None
                    new_authority = None
                else:
                    raise ValueError(f"Unknown operation: {operation.operation}")

                # 构造指令参数
                instruction_data = {
                    "authorityType": authority_type,
                    "newAuthority": new_authority
                }

                instructions.append(instruction_data)

        # 使用anchorpy构造指令
        # 注意：这里需要根据实际的anchorpy API来构造指令
        # 由于anchorpy的具体使用方式可能需要更多的上下文信息，
        # 这里提供一个基本的框架

        # 构造Solana合约调用参数
        # 注意：TokenizationSolContractCallParams可能需要从具体的模块导入
        # 这里先使用一个通用的方法来构造Solana指令

        # 暂时使用字典格式，实际使用时需要根据cobo_waas2的具体API调整
        param_data = {
            "type": "SOL_Contract",
            "instructions": instructions,  # 这里需要根据实际的格式调整
        }

        sdk_request = TokenizationContractCallRequest(
            source=req.tx_detail.source,
            data=TokenizationContractCallParamsData.from_dict(param_data),
            fee=req.est_fee,
            app_initiator=user_email,
        )

        resp = api.tokenization_contract_call(
            token_id=req.token_id, tokenization_contract_call_request=sdk_request
        )

        return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def get_restricted_access_api(
        cls, org_id: str, req: RestrictedAccessRequest
    ) -> RestrictedAccessResponse:
        """查询受限访问状态"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.get_tokenization_allowlist_activation(token_id=req.token_id)
            return RestrictedAccessResponse(switch=resp.activated)

    @classmethod
    def update_restricted_access_api(
        cls, org_id: str, req: UpdateRestrictedAccessRequest
    ) -> CommonOperationResponse:
        """更新受限访问状态"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.update_tokenization_allowlist_activation(
                token_id=req.token_id,
                tokenization_allowlist_activation_request=req.tx_detail,
            )
            return CommonOperationResponse(id=resp.activity_id)

    @classmethod
    def estimate_operation_fee_api(
        cls, org_id: str, req: EstimateFeeRequest
    ) -> TransactionRequestFee:
        """预估操作费用"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.estimate_tokenization_fee(
                tokenization_estimate_fee_request=TokenizationEstimateFeeRequest(
                    operation_params=req.operation_params
                ),
            )

            return TransactionRequestFee.from_dict(
                {
                    "fee_type": resp.actual_instance.fee_type,
                    "token_id": resp.actual_instance.token_id,
                    **resp.actual_instance.fast.to_dict(),
                }
            )

    @classmethod
    def get_user_address_list_api(
        cls, org_id: str, req: UserAddressListRequest
    ) -> UserAddressListResponse:
        """获取用户地址列表"""
        with CoboAppApiClientManager(org_id)._get_client() as client:
            addresses = (
                WalletApi(client)
                .list_wallet_address(
                    chain_id=req.chain_id,
                    wallet_subtype=req.wallet_subtype,
                    wallet_type=req.wallet_type,
                    current_page=1,
                    page_size=999999,
                )
                .data
            )

            with WaaSDevApiClientManager.get_client(org_id) as waas2_client:
                permission_address = None
                if req.operation_type is not None and req.token_id is not None:
                    permission_address = []
                    resp = cobo_waas2.TokenizationApi(
                        waas2_client
                    ).get_tokenization_info(token_id=req.token_id)
                    for item in resp.permissions:
                        if req.operation_type in item.permissions:
                            permission_address.append(item.execution_address.lower())

                def _filter_by_permission(address: AppAddressData) -> bool:
                    if permission_address is None:
                        return True
                    return address.address.lower() in permission_address

                addresses = list(filter(_filter_by_permission, addresses))

                return UserAddressListResponse(addresses=addresses)

    @classmethod
    def get_transaction_history_api(
        cls, org_id: str, req: TransactionHistoryRequest
    ) -> TransactionHistoryResponse:
        """获取交易历史"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TransactionsApi(client)

            token = cobo_waas2.TokenizationApi(client).get_tokenization_info(
                token_id=req.token_id
            )
            if not token:
                raise Exception("Token not found")

            id_to_initiators = {}

            transaction_ids = []
            activities = paginated_request(
                cobo_waas2.TokenizationApi(client).list_tokenization_activities,
                token_id=req.token_id,
            )

            for activity in activities:
                transaction_ids.extend(activity.transaction_ids)
                for transaction_id in activity.transaction_ids:
                    id_to_initiators[transaction_id] = activity.initiator
            if not transaction_ids:
                return TransactionHistoryResponse(
                    transactions=[],
                    pagination=PaginationInfo(page_size=0, current=0, total_count=0),
                )

            data = paginated_request(
                api.list_transactions,
                statuses=req.filter.status if req.filter else None,
                transaction_ids=str.join(",", transaction_ids),
                direction=req.direction,
            )

            transactions: List[Transaction] = []

            wallet_ids = set()
            for x in data:
                if hasattr(x.source.actual_instance, "wallet_id"):
                    wallet_ids.add(x.source.actual_instance.wallet_id)
                if hasattr(x.destination.actual_instance, "wallet_id"):
                    wallet_ids.add(x.destination.actual_instance.wallet_id)

            wallet_name_map = WalletController.query_wallet_names(
                org_id, list(wallet_ids)
            )

            for item in data:
                dest = item.destination.actual_instance
                source = item.source.actual_instance
                call_data = dest.calldata
                functions: List[FunctionsData] = []

                if req.filter:
                    if (
                        req.filter.creat_time_before
                        and item.created_timestamp > req.filter.creat_time_before
                    ):
                        continue

                    if (
                        req.filter.creat_time_after
                        and item.created_timestamp < req.filter.creat_time_after
                    ):
                        continue

                if req.keyword is not None and req.keyword != "":
                    if req.keyword not in [
                        item.transaction_hash,
                        item.request_id,
                        item.transaction_id,
                        source.address,
                    ]:
                        continue

                # 从calldata中提取selector
                selector = get_selector_from_calldata(call_data)

                # 使用智能解析函数，根据 selector 自动选择正确的 ABI
                decoded_result = smart_decode_function_input(call_data)
                if decoded_result is None:
                    logger.warning(f"Failed to decode calldata: {call_data}")
                    continue

                func, functions_data = decoded_result
                func_name = func.fn_name

                # 递归处理函数调用，包括multicall
                functions = cls._process_function_calls(
                    selector, func_name, functions_data, token.decimals
                )

                amount = None
                if len(functions) == 1:
                    amount = extract_amount_from_function(selector, functions[0].json)

                if (
                    req.filter is None
                    or req.filter.type is None
                    or req.filter.type.lower() == func_name.lower()
                ):
                    transactions.append(
                        Transaction(
                            status=item.status,
                            type=func_name,
                            create_time=item.created_timestamp,
                            update_time=item.updated_timestamp,
                            tx_hash=item.transaction_hash,
                            initiated_address=source.address,
                            target_address=dest.address,
                            amount=amount,
                            target_wallet_id=dest.wallet_id
                            if hasattr(dest, "wallet_id")
                            else "",
                            target_wallet=wallet_name_map.get(dest.wallet_id, "")
                            if hasattr(dest, "wallet_id")
                            else "",
                            initiated_wallet_id=source.wallet_id
                            if hasattr(source, "wallet_id")
                            else "",
                            initiated_wallet=wallet_name_map.get(source.wallet_id, "")
                            if hasattr(source, "wallet_id")
                            else "",
                            chain_icon=None,
                            token_icon=None,
                            transaction_id=item.transaction_id,
                            request_id=item.request_id,
                            cobo_id=item.cobo_id,
                            initiator=id_to_initiators.get(item.transaction_id),
                            network_fee=item.fee,  # item.fee,
                            functions=functions,
                            tx_hash_scan_url=settings.SCAN_URL.get(item.chain_id)
                            + "tx/"
                            + item.transaction_hash
                            if item.transaction_hash is not None
                            else "",
                        )
                    )

            pag = PaginationInfo(
                page_size=req.page_size,
                current=req.current + 1,
                total_count=len(transactions),
            )

            start = req.current * req.page_size
            end = (req.current + 1) * req.page_size

            if start >= len(transactions):
                return TransactionHistoryResponse(transactions=[], pagination=pag)

            return TransactionHistoryResponse(
                transactions=transactions[start:end],
                pagination=pag,
            )

    @classmethod
    def get_transaction_details_api(
        cls, org_id: str, req: TransactionDetailsRequest
    ) -> TransactionDetailResponse:
        """获取交易历史详情"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TransactionsApi(client)
            detail = api.get_transaction_by_id(req.transaction_id)

            return TransactionDetailResponse(
                detail=detail,
                tx_hash_scan_url=settings.SCAN_URL.get(detail.chain_id)
                + "tx/"
                + detail.transaction_hash
                if detail.transaction_hash is not None
                and settings.SCAN_URL.get(detail.chain_id)
                else "",
            )

    @classmethod
    def get_function_name(cls, calldata: str) -> str | None:
        # 解析 calldata 获取方法选择器
        if len(calldata) < 10:  # 0x + 8位选择器
            return None

        selector = calldata[2:10]
        function_name = get_name_by_selector(selector)

        return function_name

    @classmethod
    def get_wallet_balance_api(
        cls, org_id: str, req: WalletBalanceRequest
    ) -> WalletBalanceResponse:
        """获取钱包余额"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            resp = api.list_tokenization_holdings(token_id=req.token_id)
            if not resp.data:
                return WalletBalanceResponse(
                    wallet_address=req.wallet_address,
                    balance="0",
                    token="",
                )

            for item in resp.data:
                if item.address == req.wallet_address:
                    return WalletBalanceResponse(
                        wallet_address=req.wallet_address,
                        balance=str(item.balance),
                        token=req.token_id,
                    )

            return WalletBalanceResponse(
                wallet_address=req.wallet_address,
                balance="0",
                token="",
            )

    @classmethod
    def delete_token_api(
        cls, org_id: str, req: DeleteTokenRequest
    ) -> CommonOperationResponse:
        """获取钱包余额"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            cobo_waas2.TokenizationApi(client)

            return CommonOperationResponse(id="123")

    @classmethod
    def get_token_info(cls, req: TokenInfoRequest) -> TokenInfoResponse:
        token_info = TokenManager.get_token_info(token_id=req.token_id)
        return TokenInfoResponse(
            token_id=token_info.token_id,
            chain_id=token_info.chain_id,
            icon_url=token_info.icon_url,
            usd_rate=decimal_to_string(token_info.usd_rate),
        )

    @classmethod
    def get_token_list_api(cls, org_id: str) -> ListTokenResponse:
        """获取代币列表（带分页和过滤）"""
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)
            tokens = paginated_request(api.list_issued_tokens, limit=50)
            tokens.reverse()

            tokenInfos: List[TokenInfo] = []

            for item in tokens:
                token_info = TokenManager.get_token_info(item.token_id)
                chain_token_info = TokenManager.get_token_info(item.chain_id)

                scan_url = settings.SCAN_URL.get(item.chain_id)
                scan_icon_url = settings.SCAN_ICON_URL.get(item.chain_id)

                if item.token_address is not None:
                    scan_url = scan_url + "token/" + item.token_address
                tokenInfos.append(
                    TokenInfo(
                        token=item,
                        chain_icon_url=chain_token_info.icon_url
                        if chain_token_info is not None
                        else None,
                        token_icon_url=token_info.icon_url
                        if token_info is not None
                        else None,
                        scan_url=scan_url,
                        scan_icon_url=scan_icon_url,
                    )
                )

            return ListTokenResponse(
                pagination=cobo_waas2.Pagination(
                    before="",
                    after="",
                    total_count=len(tokens),
                ),
                data=tokenInfos,
            )

    @classmethod
    def grant_role_api(
        cls, org_id: str, token_id: str, role: str, address: str
    ) -> TokenizationOperationResponse:
        """授予角色权限

        Args:
            org_id: 组织ID
            token_id: 代币ID
            role: 角色名称
            address: 要授予权限的地址
        """
        with WaaSDevApiClientManager.get_client(org_id) as client:
            api = cobo_waas2.TokenizationApi(client)

            # 构造合约调用数据
            # grantRole(bytes32 role, address account)
            # role需要keccak256哈希
            role_hash = Web3.keccak(text=role).hex()

            # 构造calldata
            function_signature = "grantRole(bytes32,address)"
            function_selector = Web3.keccak(text=function_signature)[:4].hex()

            # 参数编码:
            # 1. role: bytes32 - 补齐到32字节
            # 2. address: address - 补齐到32字节
            role_param = role_hash[2:].rjust(64, "0")  # 去掉0x前缀,补齐到32字节
            address_param = address[2:].rjust(64, "0")  # 去掉0x前缀,补齐到32字节

            calldata = f"0x{function_selector}{role_param}{address_param}"

            # 构造请求参数
            request = TokenizationContractCallRequest(
                data=TokenizationContractCallParamsData(
                    TokenizationEvmContractCallParams(
                        type="EVM_Contract", calldata=calldata, value="0"  # 不需要转账value
                    )
                ),
                fee=TransactionRequestFee(fee_rate="medium"),
            )

            # 调用合约
            return api.tokenization_contract_call(
                token_id=token_id, tokenization_contract_call_request=request
            )

    @classmethod
    def _process_function_calls(
        cls,
        selector: str,
        func_name: str,
        functions_data: Dict[str, Any],
        decimals: int,
    ) -> List[FunctionsData]:
        """
        递归处理函数调用，包括multicall的情况

        Args:
            selector: 函数selector
            func_name: 函数名称
            functions_data: 解码后的函数参数
            decimals: 代币小数位数

        Returns:
            处理后的函数调用列表
        """
        functions: List[FunctionsData] = []

        # 处理参数
        processed_params = process_params_by_selector(
            selector, functions_data, decimals
        )

        if func_name == "multicall" and "data" in processed_params:
            # multicall情况：子调用已经在process_params_by_selector中处理过了
            for sub_call in processed_params["data"]:
                if (
                    isinstance(sub_call, dict)
                    and "function" in sub_call
                    and "params" in sub_call
                ):
                    sub_func_name = sub_call["function"]
                    sub_params = sub_call["params"]

                    functions.append(FunctionsData(type=sub_func_name, json=sub_params))
                else:
                    # 如果子调用格式不正确，记录错误但继续处理
                    logger.warning(f"Invalid sub-call format in multicall: {sub_call}")

        else:
            # 普通函数调用
            functions.append(FunctionsData(type=func_name, json=processed_params))

        return functions
