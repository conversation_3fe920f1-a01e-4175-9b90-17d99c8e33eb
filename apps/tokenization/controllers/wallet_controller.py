import logging
from typing import Dict, List

import cobo_waas2

from apps.clients.waas_dev_client import WaaSDevApiClientManager

logger = logging.getLogger("tokenization")


class WalletController:
    @classmethod
    def query_wallet_names(cls, org_id: str, wallet_ids: List[str]) -> Dict[str, str]:
        """查询代币余额"""
        if not wallet_ids:
            return {}
        graphql = ""
        for i in range(len(wallet_ids)):
            graphql += f"""
            wallet_{i+1}: wallet(walletId: "{wallet_ids[i]}") {{
                name
                walletId
            }}
        """
        graphql = f"""
        query getMultipleWallets {{
            {graphql}
        }}
        """

        with WaaSDevApiClientManager.get_client(org_id) as client:
            resp = cobo_waas2.GraphQLApi(client).execute_graphql(
                cobo_waas2.GraphQLRequest(query=graphql)
            )

        result = {}
        for wallet_info in resp.data.values():
            result[wallet_info["walletId"]] = wallet_info["name"]
        return result
