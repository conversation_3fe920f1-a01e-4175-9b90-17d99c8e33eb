# Portal Tokenization App - waas 模块

## 模块定位

waas 模块是一个简单的内部工具，主要用于：
1. 维护 WaaS (Wallet-as-a-Service) 系统的 OpenAPI 定义文件
2. 通过脚本自动生成与 WaaS 系统交互的客户端代码

**重要说明：** 这里的接口仅供系统内部使用，不是对外开放的公共 API。这些接口专门用于 Portal Tokenization 内部组件与 WaaS 服务之间的通信。

## 系统集成关系

waas 模块生成的代码主要被 `apps.clients.waas_internal_client.py` 使用，形成了以下工作流程：

```mermaid
graph LR
    A[OpenAPI定义文件] --> B[gen_client脚本]
    B --> C[生成的客户端代码]
    C --> D[apps.clients.waas_internal_client]
    D --> E[业务逻辑]
```

与此相对的是，`apps.clients.waas_dev_client.py` 使用的是通过 `requirements.txt` 中的 `cobo-waas2-python-api` 依赖引入的官方SDK，用于连接外部WaaS系统。

## 使用方法

### 1. 维护 OpenAPI 文件

OpenAPI 定义文件位于 `client` 目录中，用于描述 WaaS 系统的内部 API 接口。当 WaaS 系统内部 API 发生变更时，只需更新该定义文件。

### 2. 生成客户端代码

使用 `client` 目录下的 `gen_client` 脚本来自动创建内部客户端代码：

```bash
# 执行生成脚本(相对路径操作清空和移动代码)
cd apps/waas/client
./gen_client
```

这个脚本使用 openapi-generator-cli 工具基于 OpenAPI 定义文件生成客户端代码，过程包括：
- 清理旧的生成代码
- 基于 openapi.yaml 生成新的 Python 客户端
- 复制生成的代码到指定位置

### 3. 生成后的使用

生成的代码不需要直接调用。一般通过 `apps.clients.waas_internal_client.py` 提供的接口间接使用：

```python
# 推荐的使用方式 - 通过clients模块调用
from apps.clients.waas_internal_client import WaaSInternalApiClientManager

client = WaaSInternalApiClientManager.get_web_client(org_id="your_org_id")
# 使用client调用WaaS API...
```

## 注意事项

1. **不要手动修改生成的代码** - 所有更改应在 OpenAPI 定义文件中进行
2. **依赖工具** - 确保已安装 openapi-generator-cli 工具，这是代码生成的必要依赖
3. **更新流程** - 更新OpenAPI文件并重新生成代码后，确保 `apps.clients.waas_internal_client.py` 仍能正常工作
