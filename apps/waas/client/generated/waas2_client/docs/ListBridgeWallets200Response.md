# ListBridgeWallets200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**List[BridgeWallet]**](BridgeWallet.md) |  | [optional] 
**pagination** | [**Pagination**](Pagination.md) |  | [optional] 

## Example

```python
from apps.waas.client.generated.waas2_client.models.list_bridge_wallets200_response import ListBridgeWallets200Response

# TODO update the JSON string below
json = "{}"
# create an instance of ListBridgeWallets200Response from a JSON string
list_bridge_wallets200_response_instance = ListBridgeWallets200Response.from_json(json)
# print the JSON string representation of the object
print(ListBridgeWallets200Response.to_json())

# convert the object into a dict
list_bridge_wallets200_response_dict = list_bridge_wallets200_response_instance.to_dict()
# create an instance of ListBridgeWallets200Response from a dict
list_bridge_wallets200_response_from_dict = ListBridgeWallets200Response.from_dict(list_bridge_wallets200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


