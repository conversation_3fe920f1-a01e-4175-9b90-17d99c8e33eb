# Token

The token data for immutable token data.

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**token_id** | **str** | The token identifier, Unique in all chains scope. | 
**chain_id** | **str** | The blockchain on which the chain operates. | 
**symbol** | **str** | symbol for the token. | [optional] 
**name** | **str** | name of the token. | [optional] 
**icon_url** | **str** | The icon url of the coin. | [optional] 
**decimal** | **int** | Number of decimal places the token supports. | 
**fee_token_id** | **str** | Token used for transaction fee. | 
**confirming_threshold** | **int** | Number of confirmations required for a transaction, such as 64 for ETH chain. | 
**token_address** | **str** | Address for tokens, if applicable. | [optional] 
**dust_threshold** | **int** | Minimum amount of token that can be transacted, such as 546 for BTC. | [optional] 
**minimum_deposit_threshold** | **int** | Minimum amount of token that can be deposit, such as 10000 for BTC. | [optional] 
**require_memo** | **bool** | Indicates if a memo is required for transactions. The memo is mostly used for some tokens like XLM, XRP, EOS, etc. | [optional] [default to False]
**usd_rate** | **float** | The USD rate of the token. | [default to 0]
**asset_id** | **str** | The asset identifier. | [optional] 

## Example

```python
from apps.waas.client.generated.waas2_client.models.token import Token

# TODO update the JSON string below
json = "{}"
# create an instance of Token from a JSON string
token_instance = Token.from_json(json)
# print the JSON string representation of the object
print(Token.to_json())

# convert the object into a dict
token_dict = token_instance.to_dict()
# create an instance of Token from a dict
token_from_dict = Token.from_dict(token_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


