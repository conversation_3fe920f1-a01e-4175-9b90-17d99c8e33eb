# GetActivityDetail200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**activity_id** | **str** |  | [optional] 
**request_id** | **str** |  | [optional] 
**timeline** | [**List[ActivityTimelineItem]**](ActivityTimelineItem.md) |  | [optional] 

## Example

```python
from apps.waas.client.generated.waas2_client.models.get_activity_detail200_response import GetActivityDetail200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetActivityDetail200Response from a JSON string
get_activity_detail200_response_instance = GetActivityDetail200Response.from_json(json)
# print the JSON string representation of the object
print(GetActivityDetail200Response.to_json())

# convert the object into a dict
get_activity_detail200_response_dict = get_activity_detail200_response_instance.to_dict()
# create an instance of GetActivityDetail200Response from a dict
get_activity_detail200_response_from_dict = GetActivityDetail200Response.from_dict(get_activity_detail200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


