# ErrorResponse

The data for error response.

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**success** | **bool** | Indicates if the API operation was successful. Always false for errors. | 
**error_code** | **int** | A machine-readable error code.&#x60; | 
**error_message** | **str** | A human-readable error description for users. | [optional] 
**error_id** | **str** | A unique ID for the error log, mainly used for debugging. | 

## Example

```python
from apps.waas.client.generated.waas2_client.models.error_response import ErrorResponse

# TODO update the JSON string below
json = "{}"
# create an instance of ErrorResponse from a JSON string
error_response_instance = ErrorResponse.from_json(json)
# print the JSON string representation of the object
print(ErrorResponse.to_json())

# convert the object into a dict
error_response_dict = error_response_instance.to_dict()
# create an instance of ErrorResponse from a dict
error_response_from_dict = ErrorResponse.from_dict(error_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


