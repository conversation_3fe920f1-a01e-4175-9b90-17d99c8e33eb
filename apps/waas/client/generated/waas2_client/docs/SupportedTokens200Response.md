# SupportedTokens200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**success** | **bool** | Indicates if the api operation was successful | 
**result** | [**List[Token]**](Token.md) |  | [optional] 

## Example

```python
from apps.waas.client.generated.waas2_client.models.supported_tokens200_response import SupportedTokens200Response

# TODO update the JSON string below
json = "{}"
# create an instance of SupportedTokens200Response from a JSON string
supported_tokens200_response_instance = SupportedTokens200Response.from_json(json)
# print the JSON string representation of the object
print(SupportedTokens200Response.to_json())

# convert the object into a dict
supported_tokens200_response_dict = supported_tokens200_response_instance.to_dict()
# create an instance of SupportedTokens200Response from a dict
supported_tokens200_response_from_dict = SupportedTokens200Response.from_dict(supported_tokens200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


