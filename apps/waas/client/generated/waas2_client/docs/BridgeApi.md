# apps.waas.client.generated.waas2_client.BridgeApi

All URIs are relative to *https://api.cobo.com/v2*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_activity_detail**](BridgeApi.md#get_activity_detail) | **GET** /swaps/apps/bridge/activities/{activity_id} | Get activity detail.
[**list_bridge_wallets**](BridgeApi.md#list_bridge_wallets) | **GET** /swaps/apps/bridge/wallets | List bridge wallet infomations.


# **get_activity_detail**
> GetActivityDetail200Response get_activity_detail(activity_id)

Get activity detail.

Retrieve a list of bridge wallets.

### Example

* OAuth Authentication (OAuth2):

```python
import apps.waas.client.generated.waas2_client
from apps.waas.client.generated.waas2_client.models.get_activity_detail200_response import GetActivityDetail200Response
from apps.waas.client.generated.waas2_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.cobo.com/v2
# See configuration.py for a list of all supported configuration parameters.
configuration = apps.waas.client.generated.waas2_client.Configuration(
    host = "https://api.cobo.com/v2"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

configuration.access_token = os.environ["ACCESS_TOKEN"]

# Enter a context with an instance of the API client
with apps.waas.client.generated.waas2_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = apps.waas.client.generated.waas2_client.BridgeApi(api_client)
    activity_id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479' # str | The activity ID.

    try:
        # Get activity detail.
        api_response = api_instance.get_activity_detail(activity_id)
        print("The response of BridgeApi->get_activity_detail:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling BridgeApi->get_activity_detail: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **activity_id** | **str**| The activity ID. | 

### Return type

[**GetActivityDetail200Response**](GetActivityDetail200Response.md)

### Authorization

[OAuth2](../README.md#OAuth2)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successfully listed extra details |  -  |
**400** | Bad Request |  -  |
**404** | Resource Not Found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_bridge_wallets**
> ListBridgeWallets200Response list_bridge_wallets(limit=limit, before=before, after=after)

List bridge wallet infomations.

Retrieve a list of bridge wallets.

### Example

* OAuth Authentication (OAuth2):

```python
import apps.waas.client.generated.waas2_client
from apps.waas.client.generated.waas2_client.models.list_bridge_wallets200_response import ListBridgeWallets200Response
from apps.waas.client.generated.waas2_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.cobo.com/v2
# See configuration.py for a list of all supported configuration parameters.
configuration = apps.waas.client.generated.waas2_client.Configuration(
    host = "https://api.cobo.com/v2"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

configuration.access_token = os.environ["ACCESS_TOKEN"]

# Enter a context with an instance of the API client
with apps.waas.client.generated.waas2_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = apps.waas.client.generated.waas2_client.BridgeApi(api_client)
    limit = 10 # int | The maximum number of objects to return. For most operations, the value range is [1, 50]. (optional) (default to 10)
    before = 'RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1' # str | An object ID that serves as a starting point for retrieving data in reverse chronological order. For example, if you specify `before` as `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1`, the request will retrieve a list of data objects that end before the object with the object ID `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1`. You can set this parameter to the value of `pagination.before` in the response of the previous request.  - If you set both `after` and `before`, an error will occur.  - If you leave both `before` and `after` empty, the first page of data is returned.  - If you set `before` to `infinity`, the last page of data is returned.  (optional)
    after = 'RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk' # str | An object ID that acts as a starting point for retrieving data in chronological order. For example, if you specify `after` as `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk`, the request will retrieve a list of data objects that start after the object with the object ID `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk`. You can set this parameter to the value of `pagination.after` in the response of the previous request.  - If you set both `after` and `before`, an error will occur.  - If you leave both `before` and `after` empty, the first page of data is returned.  (optional)

    try:
        # List bridge wallet infomations.
        api_response = api_instance.list_bridge_wallets(limit=limit, before=before, after=after)
        print("The response of BridgeApi->list_bridge_wallets:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling BridgeApi->list_bridge_wallets: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**| The maximum number of objects to return. For most operations, the value range is [1, 50]. | [optional] [default to 10]
 **before** | **str**| An object ID that serves as a starting point for retrieving data in reverse chronological order. For example, if you specify &#x60;before&#x60; as &#x60;RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1&#x60;, the request will retrieve a list of data objects that end before the object with the object ID &#x60;RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1&#x60;. You can set this parameter to the value of &#x60;pagination.before&#x60; in the response of the previous request.  - If you set both &#x60;after&#x60; and &#x60;before&#x60;, an error will occur.  - If you leave both &#x60;before&#x60; and &#x60;after&#x60; empty, the first page of data is returned.  - If you set &#x60;before&#x60; to &#x60;infinity&#x60;, the last page of data is returned.  | [optional] 
 **after** | **str**| An object ID that acts as a starting point for retrieving data in chronological order. For example, if you specify &#x60;after&#x60; as &#x60;RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk&#x60;, the request will retrieve a list of data objects that start after the object with the object ID &#x60;RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk&#x60;. You can set this parameter to the value of &#x60;pagination.after&#x60; in the response of the previous request.  - If you set both &#x60;after&#x60; and &#x60;before&#x60;, an error will occur.  - If you leave both &#x60;before&#x60; and &#x60;after&#x60; empty, the first page of data is returned.  | [optional] 

### Return type

[**ListBridgeWallets200Response**](ListBridgeWallets200Response.md)

### Authorization

[OAuth2](../README.md#OAuth2)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successfully listed bridge wallets |  -  |
**400** | Bad Request |  -  |
**404** | Resource Not Found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

