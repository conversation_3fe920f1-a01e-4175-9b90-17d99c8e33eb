# BridgeWallet


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**wallet_id** | **str** |  | [optional] 
**wallet_type** | **str** |  | [optional] 
**wallet_subtype** | **str** |  | [optional] 
**name** | **str** |  | [optional] 
**usd_value** | **str** |  | [optional] 

## Example

```python
from apps.waas.client.generated.waas2_client.models.bridge_wallet import BridgeWallet

# TODO update the JSON string below
json = "{}"
# create an instance of BridgeWallet from a JSON string
bridge_wallet_instance = BridgeWallet.from_json(json)
# print the JSON string representation of the object
print(BridgeWallet.to_json())

# convert the object into a dict
bridge_wallet_dict = bridge_wallet_instance.to_dict()
# create an instance of BridgeWallet from a dict
bridge_wallet_from_dict = BridgeWallet.from_dict(bridge_wallet_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


