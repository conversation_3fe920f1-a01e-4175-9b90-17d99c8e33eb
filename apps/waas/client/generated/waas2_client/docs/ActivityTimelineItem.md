# ActivityTimelineItem


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**action** | **str** |  | [optional] 
**status** | **str** |  | [optional] 
**timestamp** | **int** |  | [optional] 

## Example

```python
from apps.waas.client.generated.waas2_client.models.activity_timeline_item import ActivityTimelineItem

# TODO update the JSON string below
json = "{}"
# create an instance of ActivityTimelineItem from a JSON string
activity_timeline_item_instance = ActivityTimelineItem.from_json(json)
# print the JSON string representation of the object
print(ActivityTimelineItem.to_json())

# convert the object into a dict
activity_timeline_item_dict = activity_timeline_item_instance.to_dict()
# create an instance of ActivityTimelineItem from a dict
activity_timeline_item_from_dict = ActivityTimelineItem.from_dict(activity_timeline_item_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


