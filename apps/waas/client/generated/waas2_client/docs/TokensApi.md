# apps.waas.client.generated.waas2_client.TokensApi

All URIs are relative to *https://api.cobo.com/v2*

Method | HTTP request | Description
------------- | ------------- | -------------
[**supported_tokens**](TokensApi.md#supported_tokens) | **GET** /token/tokens | List supported tokens info by wallet.


# **supported_tokens**
> SupportedTokens200Response supported_tokens(token_ids=token_ids, chain_ids=chain_ids, ignore_token_rate=ignore_token_rate)

List supported tokens info by wallet.

Retrieve a list of supported tokens.

### Example

* Api Key Authentication (CoboNonce):
* Api Key Authentication (CookieAuth):
* OAuth Authentication (OAuth2):
* Api Key Authentication (CoboAuth):
* Api Key Authentication (CoboSignature):

```python
import apps.waas.client.generated.waas2_client
from apps.waas.client.generated.waas2_client.models.supported_tokens200_response import SupportedTokens200Response
from apps.waas.client.generated.waas2_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.cobo.com/v2
# See configuration.py for a list of all supported configuration parameters.
configuration = apps.waas.client.generated.waas2_client.Configuration(
    host = "https://api.cobo.com/v2"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure API key authorization: CoboNonce
configuration.api_key['CoboNonce'] = os.environ["API_KEY"]

# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['CoboNonce'] = 'Bearer'

# Configure API key authorization: CookieAuth
configuration.api_key['CookieAuth'] = os.environ["API_KEY"]

# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['CookieAuth'] = 'Bearer'

configuration.access_token = os.environ["ACCESS_TOKEN"]

# Configure API key authorization: CoboAuth
configuration.api_key['CoboAuth'] = os.environ["API_KEY"]

# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['CoboAuth'] = 'Bearer'

# Configure API key authorization: CoboSignature
configuration.api_key['CoboSignature'] = os.environ["API_KEY"]

# Uncomment below to setup prefix (e.g. Bearer) for API key, if needed
# configuration.api_key_prefix['CoboSignature'] = 'Bearer'

# Enter a context with an instance of the API client
with apps.waas.client.generated.waas2_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = apps.waas.client.generated.waas2_client.TokensApi(api_client)
    token_ids = 'ETH_USDT,ETH_USDC' # str | A list of token IDs, separated by comma. The token ID is the unique identifier of a token. You can retrieve the IDs of all the tokens you can use by calling [List enabled tokens](/v2/api-references/wallets/list-enabled-tokens). (optional)
    chain_ids = 'BTC,ETH' # str | A list of chain IDs, separated by comma. The chain ID is the unique identifier of a blockchain. You can retrieve the IDs of all the chains you can use by calling [List enabled chains](/v2/api-references/wallets/list-enabled-chains). (optional)
    ignore_token_rate = 1 # int | Ignore token rate (optional)

    try:
        # List supported tokens info by wallet.
        api_response = api_instance.supported_tokens(token_ids=token_ids, chain_ids=chain_ids, ignore_token_rate=ignore_token_rate)
        print("The response of TokensApi->supported_tokens:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling TokensApi->supported_tokens: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **token_ids** | **str**| A list of token IDs, separated by comma. The token ID is the unique identifier of a token. You can retrieve the IDs of all the tokens you can use by calling [List enabled tokens](/v2/api-references/wallets/list-enabled-tokens). | [optional] 
 **chain_ids** | **str**| A list of chain IDs, separated by comma. The chain ID is the unique identifier of a blockchain. You can retrieve the IDs of all the chains you can use by calling [List enabled chains](/v2/api-references/wallets/list-enabled-chains). | [optional] 
 **ignore_token_rate** | **int**| Ignore token rate | [optional] 

### Return type

[**SupportedTokens200Response**](SupportedTokens200Response.md)

### Authorization

[CoboNonce](../README.md#CoboNonce), [CookieAuth](../README.md#CookieAuth), [OAuth2](../README.md#OAuth2), [CoboAuth](../README.md#CoboAuth), [CoboSignature](../README.md#CoboSignature)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successfully listed tokens |  -  |
**400** | Bad Request |  -  |
**404** | Resource Not Found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

