# coding: utf-8

# flake8: noqa
"""
    Cobo Wallet as a Service 2.0

    Cobo WaaS 2.0 enables you to programmatically access Cobo's full suite of crypto wallet technologies with powerful and flexible access controls.  # Wallet technologies - Custodial Wallet - MPC Wallet - Smart Contract Wallet (Based on Safe{Wallet})  # Risk Control technologies - Workflow - Access Control List (ACL)  # Risk Control targets - Wallet Management   - User/team and their permission management   - Risk control configurations, e.g. whitelist, blacklist, rate-limiting etc. - Blockchain Interaction   - Crypto transfer   - Smart Contract Invocation  # Important HTTPS only. RESTful, resource oriented  # Get Started Set up your APIs or get authorization  # User ID and Org ID  # Authentication and Authorization Cobo KeyAuth OAuth 2.0 OIDC  # Request and Response application/json  # Error Handling  ### Common error codes | Error Code | Description | | -- | -- | | `2000` | Internal error | | `2002` | Unsupported HTTP method | | `2003` | Missing required parameters | | `2006` | Illegal parameter format or value | | `2010` | Exceeded frequency limit | | `2020` | Missing action | | `2021` | Missing handler | | `2022` | Missing required request header | | `2023` | Verification failed | | `2024` | Authentication failed | | `2025` | Forbidden | | `2026` | Too many requests | | `2027` | Exceed quota limit | | `2028` | Not found | | `2029` | Invalid status |  ### API-specific error codes For error codes that are dedicated to a specific API, see the Error codes section in each API specification, for example, /v2/wallets.  # Rate and Usage Limiting  # Idempotent Request  # Pagination # Support [Developer Hub](https://cobo.com/developers) 

    The version of the OpenAPI document: 1.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


# import models into model package
from apps.waas.client.generated.waas2_client.models.activity_timeline_item import ActivityTimelineItem
from apps.waas.client.generated.waas2_client.models.api_response import ApiResponse
from apps.waas.client.generated.waas2_client.models.bridge_wallet import BridgeWallet
from apps.waas.client.generated.waas2_client.models.error_response import ErrorResponse
from apps.waas.client.generated.waas2_client.models.get_activity_detail200_response import GetActivityDetail200Response
from apps.waas.client.generated.waas2_client.models.list_bridge_wallets200_response import ListBridgeWallets200Response
from apps.waas.client.generated.waas2_client.models.pagination import Pagination
from apps.waas.client.generated.waas2_client.models.pagination_metadata import PaginationMetadata
from apps.waas.client.generated.waas2_client.models.supported_tokens200_response import SupportedTokens200Response
from apps.waas.client.generated.waas2_client.models.token import Token
