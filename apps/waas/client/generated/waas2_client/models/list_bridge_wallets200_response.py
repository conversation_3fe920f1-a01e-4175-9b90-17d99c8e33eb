# coding: utf-8

"""
    Cobo Wallet as a Service 2.0

    Cobo WaaS 2.0 enables you to programmatically access Cobo's full suite of crypto wallet technologies with powerful and flexible access controls.  # Wallet technologies - Custodial Wallet - MPC Wallet - Smart Contract Wallet (Based on Safe{Wallet})  # Risk Control technologies - Workflow - Access Control List (ACL)  # Risk Control targets - Wallet Management   - User/team and their permission management   - Risk control configurations, e.g. whitelist, blacklist, rate-limiting etc. - Blockchain Interaction   - Crypto transfer   - Smart Contract Invocation  # Important HTTPS only. RESTful, resource oriented  # Get Started Set up your APIs or get authorization  # User ID and Org ID  # Authentication and Authorization Cobo KeyAuth OAuth 2.0 OIDC  # Request and Response application/json  # Error Handling  ### Common error codes | Error Code | Description | | -- | -- | | `2000` | Internal error | | `2002` | Unsupported HTTP method | | `2003` | Missing required parameters | | `2006` | Illegal parameter format or value | | `2010` | Exceeded frequency limit | | `2020` | Missing action | | `2021` | Missing handler | | `2022` | Missing required request header | | `2023` | Verification failed | | `2024` | Authentication failed | | `2025` | Forbidden | | `2026` | Too many requests | | `2027` | Exceed quota limit | | `2028` | Not found | | `2029` | Invalid status |  ### API-specific error codes For error codes that are dedicated to a specific API, see the Error codes section in each API specification, for example, /v2/wallets.  # Rate and Usage Limiting  # Idempotent Request  # Pagination # Support [Developer Hub](https://cobo.com/developers) 

    The version of the OpenAPI document: 1.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List, Optional
from apps.waas.client.generated.waas2_client.models.bridge_wallet import BridgeWallet
from apps.waas.client.generated.waas2_client.models.pagination import Pagination
from typing import Optional, Set
from typing_extensions import Self

class ListBridgeWallets200Response(BaseModel):
    """
    ListBridgeWallets200Response
    """ # noqa: E501
    data: Optional[List[BridgeWallet]] = None
    pagination: Optional[Pagination] = None
    __properties: ClassVar[List[str]] = ["data", "pagination"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ListBridgeWallets200Response from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in data (list)
        _items = []
        if self.data:
            for _item in self.data:
                if _item:
                    _items.append(_item.to_dict())
            _dict['data'] = _items
        # override the default output from pydantic by calling `to_dict()` of pagination
        if self.pagination:
            _dict['pagination'] = self.pagination.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ListBridgeWallets200Response from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "data": [BridgeWallet.from_dict(_item) for _item in obj["data"]] if obj.get("data") is not None else None,
            "pagination": Pagination.from_dict(obj["pagination"]) if obj.get("pagination") is not None else None
        })
        return _obj


