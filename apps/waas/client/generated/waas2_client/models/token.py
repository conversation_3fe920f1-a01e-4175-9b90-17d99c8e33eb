# coding: utf-8

"""
    Cobo Wallet as a Service 2.0

    Cobo WaaS 2.0 enables you to programmatically access Cobo's full suite of crypto wallet technologies with powerful and flexible access controls.  # Wallet technologies - Custodial Wallet - MPC Wallet - Smart Contract Wallet (Based on Safe{Wallet})  # Risk Control technologies - Workflow - Access Control List (ACL)  # Risk Control targets - Wallet Management   - User/team and their permission management   - Risk control configurations, e.g. whitelist, blacklist, rate-limiting etc. - Blockchain Interaction   - Crypto transfer   - Smart Contract Invocation  # Important HTTPS only. RESTful, resource oriented  # Get Started Set up your APIs or get authorization  # User ID and Org ID  # Authentication and Authorization Cobo KeyAuth OAuth 2.0 OIDC  # Request and Response application/json  # Error Handling  ### Common error codes | Error Code | Description | | -- | -- | | `2000` | Internal error | | `2002` | Unsupported HTTP method | | `2003` | Missing required parameters | | `2006` | Illegal parameter format or value | | `2010` | Exceeded frequency limit | | `2020` | Missing action | | `2021` | Missing handler | | `2022` | Missing required request header | | `2023` | Verification failed | | `2024` | Authentication failed | | `2025` | Forbidden | | `2026` | Too many requests | | `2027` | Exceed quota limit | | `2028` | Not found | | `2029` | Invalid status |  ### API-specific error codes For error codes that are dedicated to a specific API, see the Error codes section in each API specification, for example, /v2/wallets.  # Rate and Usage Limiting  # Idempotent Request  # Pagination # Support [Developer Hub](https://cobo.com/developers) 

    The version of the OpenAPI document: 1.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class Token(BaseModel):
    """
    The token data for immutable token data.
    """ # noqa: E501
    token_id: StrictStr = Field(description="The token identifier, Unique in all chains scope.")
    chain_id: StrictStr = Field(description="The blockchain on which the chain operates.")
    symbol: Optional[StrictStr] = Field(default=None, description="symbol for the token.")
    name: Optional[StrictStr] = Field(default=None, description="name of the token.")
    icon_url: Optional[StrictStr] = Field(default=None, description="The icon url of the coin.")
    decimal: StrictInt = Field(description="Number of decimal places the token supports.")
    fee_token_id: StrictStr = Field(description="Token used for transaction fee.")
    confirming_threshold: StrictInt = Field(description="Number of confirmations required for a transaction, such as 64 for ETH chain.")
    token_address: Optional[StrictStr] = Field(default=None, description="Address for tokens, if applicable.")
    dust_threshold: Optional[StrictInt] = Field(default=None, description="Minimum amount of token that can be transacted, such as 546 for BTC.")
    minimum_deposit_threshold: Optional[StrictInt] = Field(default=None, description="Minimum amount of token that can be deposit, such as 10000 for BTC.")
    require_memo: Optional[StrictBool] = Field(default=False, description="Indicates if a memo is required for transactions. The memo is mostly used for some tokens like XLM, XRP, EOS, etc.")
    usd_rate: Union[StrictFloat, StrictInt] = Field(description="The USD rate of the token.")
    asset_id: Optional[StrictStr] = Field(default=None, description="The asset identifier.")
    __properties: ClassVar[List[str]] = ["token_id", "chain_id", "symbol", "name", "icon_url", "decimal", "fee_token_id", "confirming_threshold", "token_address", "dust_threshold", "minimum_deposit_threshold", "require_memo", "usd_rate", "asset_id"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of Token from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of Token from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "token_id": obj.get("token_id"),
            "chain_id": obj.get("chain_id"),
            "symbol": obj.get("symbol"),
            "name": obj.get("name"),
            "icon_url": obj.get("icon_url"),
            "decimal": obj.get("decimal"),
            "fee_token_id": obj.get("fee_token_id"),
            "confirming_threshold": obj.get("confirming_threshold"),
            "token_address": obj.get("token_address"),
            "dust_threshold": obj.get("dust_threshold"),
            "minimum_deposit_threshold": obj.get("minimum_deposit_threshold"),
            "require_memo": obj.get("require_memo") if obj.get("require_memo") is not None else False,
            "usd_rate": obj.get("usd_rate") if obj.get("usd_rate") is not None else 0,
            "asset_id": obj.get("asset_id")
        })
        return _obj


