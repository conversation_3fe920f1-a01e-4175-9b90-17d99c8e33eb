openapi: 3.0.3
info:
  title: Cobo Wallet as a Service 2.0
  description: |
    Cobo WaaS 2.0 enables you to programmatically access Cobo's full suite of
    crypto wallet technologies with powerful and flexible access controls.

    # Wallet technologies
    - Custodial Wallet
    - MPC Wallet
    - Smart Contract Wallet (Based on Safe{Wallet})

    # Risk Control technologies
    - Workflow
    - Access Control List (ACL)

    # Risk Control targets
    - Wallet Management
      - User/team and their permission management
      - Risk control configurations, e.g. whitelist, blacklist, rate-limiting etc.
    - Blockchain Interaction
      - Crypto transfer
      - Smart Contract Invocation

    # Important
    HTTPS only. RESTful, resource oriented

    # Get Started
    Set up your APIs or get authorization

    # User ID and Org ID

    # Authentication and Authorization
    Cobo KeyAuth
    OAuth 2.0
    OIDC

    # Request and Response
    application/json

    # Error Handling

    ### Common error codes
    | Error Code | Description |
    | -- | -- |
    | `2000` | Internal error |
    | `2002` | Unsupported HTTP method |
    | `2003` | Missing required parameters |
    | `2006` | Illegal parameter format or value |
    | `2010` | Exceeded frequency limit |
    | `2020` | Missing action |
    | `2021` | Missing handler |
    | `2022` | Missing required request header |
    | `2023` | Verification failed |
    | `2024` | Authentication failed |
    | `2025` | Forbidden |
    | `2026` | Too many requests |
    | `2027` | Exceed quota limit |
    | `2028` | Not found |
    | `2029` | Invalid status |

    ### API-specific error codes
    For error codes that are dedicated to a specific API, see the Error codes section in each API specification, for example, /v2/wallets.

    # Rate and Usage Limiting

    # Idempotent Request

    # Pagination
    # Support
    [Developer Hub](https://cobo.com/developers)
  termsOfService: 'https://cobo.com/waas/tos/'
  license:
    name: Apache 2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
  contact:
    name: Cobo WaaS
    url: 'https://www.cobo.com/waas'
    email: <EMAIL>
  version: 1.0.0
servers:
  - url: 'https://api.cobo.com/v2'
    description: Production environment
  - url: 'https://api.dev.cobo.com/v2'
    description: Development environment
  - url: 'https://accounts.cobo.com/api/v1'
    description: Cobo SSO service
tags:
  - name: Wallets
    description: Operations related to all wallets.
  - name: Wallets - Smart Contract Wallet
    description: Operations related to smart contract wallets.
  - name: Wallets - Exchange Wallet
    description: Operations related to exchange wallets.
  - name: Wallets - MPC Wallet
    description: Operations related to mpc wallet.
  - name: Wallet - Deprecated
    description: Deprecated Operations related to all wallets.
  - name: Organizations
    description: Operations related to organizations.
  - name: Transactions
    description: Operations related to transactions.
  - name: Transactions - Transfer
    description: Operations related to transfer.
  - name: Accounts
    description: Operations related to user's account info.
  - name: AddressBooks
    description: Operations related to address books.
  - name: Categories
    description: Operations related to categories.
  - name: Permissions
    description: Operations related to permissions.
  - name: RiskPolicies
    description: Operations related to risk policies.
  - name: RiskAddressLists
    description: Operations related to risk address lists.
  - name: RiskAddresses
    description: Operations related to risk addresses.
  - name: Developers - API Keys
    description: Operations related to API Keys.
  - name: Developers - Callback Url
    description: Operations related to Callback Url.
  - name: Developers - Webhooks
    description: Operations related to webhooks.
  - name: Authorization
    description: Operations related to access tokens.
  - name: Data
    description: 'Operations related to dashboard, packages, bill and etc.'
  - name: RiskApprovals
    description: Operations related to risk approvals.
  - name: RiskApiKeys
    description: Operations related to risk api keys.
  - name: Coins
    description: Operations related to coins.
  - name: Tokens
    description: Operations related with tokens.
  - name: CoboConnect
    description: Operations related with cobo connect.
paths:
  /token/tokens:
    get:
      tags:
        - Tokens
      operationId: supported_tokens
      summary: List supported tokens info by wallet.
      description: Retrieve a list of supported tokens.
      security:
        - OAuth2:
            - 'tokens:read'
            - admin
        - CoboAuth: []
          CoboSignature: []
          CoboNonce: []
        - CookieAuth: []
      parameters:
        - $ref: '#/components/parameters/optionalTokenListIdParam'
        - $ref: '#/components/parameters/optionalChainIdListParam'
        - $ref: '#/components/parameters/optionalIgnoreTokenRateParam'
      responses:
        '200':
          $ref: '#/components/responses/getTokensInfoResponse'
        '400':
          $ref: '#/components/responses/badRequestError'
        '404':
          $ref: '#/components/responses/notFoundError'
  /swaps/apps/bridge/wallets:
    get:
      tags:
        - Bridge
      operationId: list_bridge_wallets
      summary: List bridge wallet infomations.
      description: Retrieve a list of bridge wallets.
      security:
        - OAuth2:
            - 'wallets:read'
      parameters:
        - $ref: '#/components/parameters/limitParam'
        - $ref: '#/components/parameters/beforeParam'
        - $ref: '#/components/parameters/afterParam'
      responses:
        '200':
          $ref: '#/components/responses/listBridgeWalletsResponse'
        '400':
          $ref: '#/components/responses/badRequestError'
        '404':
          $ref: '#/components/responses/notFoundError'
  '/swaps/apps/bridge/activities/{activity_id}':
    get:
      tags:
        - Bridge
      operationId: get_activity_detail
      summary: Get activity detail.
      description: Retrieve a list of bridge wallets.
      security:
        - OAuth2: []
      parameters:
        - $ref: '#/components/parameters/activityIdPathParam'
      responses:
        '200':
          $ref: '#/components/responses/getActivityExtraDetailsResponse'
        '400':
          $ref: '#/components/responses/badRequestError'
        '404':
          $ref: '#/components/responses/notFoundError'
components:
  schemas:
    BridgeWallet:
      type: object
      properties:
        wallet_id:
          type: string
        wallet_type:
          type: string
        wallet_subtype:
          type: string
        name:
          type: string
        usd_value:
          type: string
    ActivityTimelineItem:
      type: object
      properties:
        action:
          type: string
        status:
          type: string
        timestamp:
          type: integer
    Pagination:
      type: object
      description: The pagination information of the returned data.
      required:
        - before
        - after
        - total_count
      properties:
        before:
          type: string
          example: RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1
          description: |
            An object ID that serves as a starting point for retrieving data in reverse chronological order for the next request. 

            If this property is empty, it means that you have reached the start of the data records.
        after:
          type: string
          example: RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk
          description: |
            An object ID that acts as a starting point for retrieving data in chronological order for the next request.

            If this property is empty, it means that you have reached the end of the data records.
        total_count:
          type: integer
          example: 10000
          description: 'The total number of records that match the query, across all pages.'
    Token:
      type: object
      description: The token data for immutable token data.
      required:
        - token_id
        - chain_id
        - decimal
        - fee_token_id
        - confirming_threshold
        - usd_rate
      properties:
        token_id:
          type: string
          description: 'The token identifier, Unique in all chains scope.'
          example: ETH_USDT
        chain_id:
          type: string
          description: The blockchain on which the chain operates.
          example: ETH
        symbol:
          type: string
          description: symbol for the token.
          example: USDT
        name:
          type: string
          description: name of the token.
        icon_url:
          type: string
          format: url
          description: The icon url of the coin.
          example: 'https://d.cobo.com/public/logos/USDC.png'
        decimal:
          type: integer
          format: int32
          description: Number of decimal places the token supports.
          example: 6
        fee_token_id:
          type: string
          description: Token used for transaction fee.
          example: ETH
        confirming_threshold:
          type: integer
          format: int32
          description: 'Number of confirmations required for a transaction, such as 64 for ETH chain.'
          example: 15
        token_address:
          type: string
          description: 'Address for tokens, if applicable.'
          example: '******************************************'
        dust_threshold:
          type: integer
          description: 'Minimum amount of token that can be transacted, such as 546 for BTC.'
          example: 546
        minimum_deposit_threshold:
          type: integer
          description: 'Minimum amount of token that can be deposit, such as 10000 for BTC.'
          example: 10000
        require_memo:
          type: boolean
          description: 'Indicates if a memo is required for transactions. The memo is mostly used for some tokens like XLM, XRP, EOS, etc.'
          default: false
        usd_rate:
          type: number
          format: double
          description: The USD rate of the token.
          example: 1
          default: 0
        asset_id:
          type: string
          description: The asset identifier.
          example: USDT
    ApiResponse:
      type: object
      description: The data for api response.
      required:
        - success
      properties:
        success:
          type: boolean
          description: Indicates if the api operation was successful
          example: true
        result:
          type: object
          description: Contains the actual response data
    ErrorResponse:
      type: object
      description: The data for error response.
      required:
        - success
        - error_code
        - error_id
      properties:
        success:
          type: boolean
          description: Indicates if the API operation was successful. Always false for errors.
        error_code:
          type: integer
          description: A machine-readable error code.`
          example: 1000
        error_message:
          type: string
          description: A human-readable error description for users.
          example: API params is missing or null
        error_id:
          type: string
          description: 'A unique ID for the error log, mainly used for debugging.'
          example: 0b6ddf19083c4bd1a9ca01bec44b24dd
    PaginationMetadata:
      type: object
      description: The meta data for pagination.
      properties:
        total_records:
          type: integer
          format: int32
          example: 20
        total_pages:
          type: integer
          format: int32
          example: 10
        current_page:
          type: integer
          format: int32
          example: 1
        page_size:
          type: integer
          format: int32
          example: 10
  parameters:
    orgIdHeaderParam:
      name: BIZ-ORG-ID
      in: header
      description: Unique org id currently being used
      required: true
      schema:
        type: string
      example: f47ac10b-58cc-4372-a567-0e02b2c3d479
    limitParam:
      name: limit
      in: query
      description: 'The maximum number of objects to return. For most operations, the value range is [1, 50].'
      required: false
      schema:
        type: integer
        format: int32
        default: 10
      example: 10
    beforeParam:
      name: before
      in: query
      description: |
        An object ID that serves as a starting point for retrieving data in reverse chronological order. For example, if you specify `before` as `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1`, the request will retrieve a list of data objects that end before the object with the object ID `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1`. You can set this parameter to the value of `pagination.before` in the response of the previous request.

        - If you set both `after` and `before`, an error will occur.

        - If you leave both `before` and `after` empty, the first page of data is returned.

        - If you set `before` to `infinity`, the last page of data is returned.
      required: false
      schema:
        type: string
      example: RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1
    afterParam:
      name: after
      in: query
      description: |
        An object ID that acts as a starting point for retrieving data in chronological order. For example, if you specify `after` as `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk`, the request will retrieve a list of data objects that start after the object with the object ID `RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk`. You can set this parameter to the value of `pagination.after` in the response of the previous request.

        - If you set both `after` and `before`, an error will occur.

        - If you leave both `before` and `after` empty, the first page of data is returned.
      required: false
      schema:
        type: string
      example: RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk
    activityIdPathParam:
      name: activity_id
      in: path
      required: true
      description: The activity ID.
      schema:
        type: string
      example: f47ac10b-58cc-4372-a567-0e02b2c3d479
    optionalTokenListIdParam:
      name: token_ids
      in: query
      required: false
      description: 'A list of token IDs, separated by comma. The token ID is the unique identifier of a token. You can retrieve the IDs of all the tokens you can use by calling [List enabled tokens](/v2/api-references/wallets/list-enabled-tokens).'
      schema:
        type: string
        description: 'A list of token IDs, separated by comma.'
      example: 'ETH_USDT,ETH_USDC'
    optionalChainIdListParam:
      name: chain_ids
      in: query
      required: false
      description: 'A list of chain IDs, separated by comma. The chain ID is the unique identifier of a blockchain. You can retrieve the IDs of all the chains you can use by calling [List enabled chains](/v2/api-references/wallets/list-enabled-chains).'
      schema:
        type: string
        description: 'A list of chain IDs, separated by comma.'
      example: 'BTC,ETH'
    optionalIgnoreTokenRateParam:
      name: ignore_token_rate
      in: query
      required: false
      description: 'Ignore token rate'
      schema:
        type: integer
      example: 1
  responses:
    badRequestError:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    unauthorizedError:
      description: Unauthorized Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    forbiddenError:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    notFoundError:
      description: Resource Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    tooManyRequestError:
      description: Too Many Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    serviceUnavailableError:
      description: Unknown Internal Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    getTokensInfoResponse:
      description: Successfully listed tokens
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ApiResponse'
              - type: object
                properties:
                  result:
                    type: array
                    items:
                      $ref: '#/components/schemas/Token'
    listBridgeWalletsResponse:
      description: Successfully listed bridge wallets
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  $ref: '#/components/schemas/BridgeWallet'
              pagination:
                $ref: '#/components/schemas/Pagination'
    getActivityExtraDetailsResponse:
      description: Successfully listed extra details
      content:
        application/json:
          schema:
            type: object
            properties:
              activity_id:
                type: string
              request_id:
                type: string
              timeline:
                type: array
                items:
                  $ref: '#/components/schemas/ActivityTimelineItem'
  securitySchemes:
    OpenID:
      type: openIdConnect
      openIdConnectUrl: 'https://auth.cobo.com/.well-known/openid-configuration'
    OAuth2:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: 'https://auth.cobo.com/authorize'
          tokenUrl: 'https://auth.cobo.com/oauth/token'
          scopes:
            'wallets:read': Read access to wallets
            'wallets:balance:read': Read access to wallet balances
            'wallets:address:read': Read access to wallet addresses
            'wallets:write': Write access to wallets
            'wallets:transfer': Transfer crypto out of the wallets
            'wallets:smart_contract_call': smart contract call from the wallets
            'transactions:read': Read access to transactions
            'transactions:write': Write access to transactions
            'organizations:read': Read access to organizations
            'organizations:write': Write access to organizations
            'associations:read': Read access to associations
            'associations:write': Write access to associations
            'webhooks:read': Read access to webhooks
            'webhooks:write': Write access to webhooks
            admin: Grants access to admin operations
            'address_book:create': Create access to address book
            'address_book:read': Get access to address book
            'address_book:update': Update access to address book
            'address_book:delete': Delete access to address book
            'categories:read': Read access to categories
            'categories:write': Write access to categories
            'risk_address_list:create': Create access to risk address list
            'risk_address_list:read': Get access to risk address list
            'risk_address_list:update': Update access to risk address list
            'risk_addresses:create': Create access to risk addresses
            'risk_addresses:read': Get access to risk addresses
            'risk_addresses:delete': Delete access to risk addresses
            'risk_policy:create': Create access to risk policy
            'risk_policy:read': Get access to risk policy
            'risk_policy:update': Update access to risk policy
            'risk_policy:delete': Delete access to risk policy
            'risk_approval:read': Get access to risk approval
            'risk_api_key:read': Get access to risk api key
            'coins:read': Read access to coins
    CoboAuth:
      type: apiKey
      in: header
      name: BIZ-API-KEY
      description: This field contains the API key.
    CoboSignature:
      type: apiKey
      in: header
      name: BIZ-API-SIGNATURE
      description: 'This field contains the EDDSA(ED25519) signature, generated using HTTP_METHOD, HTTP_REQUEST_PATH, TIMESTAMP, and PARAMS.'
    CoboNonce:
      type: apiKey
      in: header
      name: BIZ-API-NONCE
      description: The UNIX EPOCH timestamp when calling the API in milliseconds.
    CookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
      description: This field contains cookie of logged in users.
security:
  - CoboAuth: []
    CoboSignature: []
    CoboNonce: []
  - CookieAuth: []
