from enum import IntEnum, unique

from apps.utils.enums import StrEnum


@unique
class UserSessionStatus(IntEnum):
    NORMAL = 0
    FORBID = 1
    LOGOUT = 2


@unique
class PortalUserRole(StrEnum):
    Admin = "Admin"
    Operator = "Operator"
    Spender = "Spender"
    Viewer = "Viewer"
    Approver = "Approver"
    Manager = "Manager"
    Staker = "Staker"


class TokenizationFunction(StrEnum):
    MINT = "mint"
    BURN = "burn"
    ENABLE = "enable"
    DISABLE = "disable"
    RESTRICTED = "restricted"
    ACCESS = "access"
    ALLOWLIST = "allowlist"
    BLOCKLIST = "blocklist"
    PERMISSIONS = "permissions"
    PAUSED = "paused"
    UNPAUSED = "unpaused"
    MORE_ACTION_WRITE = "more_action_write"


class AppWorkflowFunction(StrEnum):
    MINT = "mint"
    BURN = "burn"
    MANAGE_TOKEN_ACCESS = "manage_token_access"
    MANAGE_BLOCKLIST = "manage_blocklist"
    MANAGE_PERMISSION = "manage_permission"
    PAUSE_AND_UNPAUSE = "pause_and_unpause"
    MORE_ACTION = "more_action"
