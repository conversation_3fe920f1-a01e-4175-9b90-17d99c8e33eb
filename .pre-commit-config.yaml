repos:
-   repo: local
    hooks:
      - id: django-upgrade
        name: django-upgrade
        entry: bash -c 'git ls-files -z -- "*.py" | xargs -0 django-upgrade --target-version 4.2 --skip django_urls'
        language: system
        files: \.py$

-   repo: https://github.com/pycqa/isort
    rev: 5.11.5
    hooks:
    -   id: isort
        name: isort (python)
        exclude: ^apps/waas/client/

-   repo: https://github.com/psf/black
    rev: 23.10.1
    hooks:
    -   id: black
        language_version: python3
        exclude: ^apps/waas/client/|/(\\.eggs|\\.git|\\.hg|\\.mypy_cache|\\.nox|\\.tox|\\.venv|_build|buck-out|build|dist|migrations|_pb2|pycoin)/

-   repo: https://github.com/pycqa/flake8
    rev: 3.8.3
    hooks:
    -   id: flake8
        language_version: python3.10
        exclude: ^apps/waas/client/|/(migrations|_pb2)/
        additional_dependencies: ["importlib-metadata==4.13.0"]

-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.1.0
    hooks:
    -   id: end-of-file-fixer
        exclude: ^apps/waas/client/|/(migrations|_pb2|pycoin|deprecated)/
        files: .*.py$
    -   id: trailing-whitespace
        exclude: ^apps/waas/client/|/(migrations|_pb2|pycoin|deprecated)/
        files: .*.py$
    -   id: file-contents-sorter
        files: ^(requirements.*.txt)$

-   repo: https://github.com/PyCQA/bandit
    rev: 1.6.2
    hooks:
    -   id: bandit
        language_version: python3
        exclude: ^apps/waas/client/|/(migrations|_pb2|pycoin|deprecated)/
        args: [ -r,  -c ,".bandit_scan.cfg", -f , "txt",  -o , "bandit_scan.log",  --baseline, ".bandit_baseline.json" , $(git ls-files) ]
        additional_dependencies: ["importlib-metadata==4.13.0"]
