import os

import pytest


@pytest.fixture(scope="session")
def django_db_keepdb(request):
    """
    This fixture requires pytest-django plugin installed.
    Local unit testing, enforce "--reuse-db" options from pytest-django plugin
    This option will prevent migration from starting over again each time, which
    will accelerate the test process.
    """
    return os.environ.get("RUN_ENV", "dev") != "ci"
