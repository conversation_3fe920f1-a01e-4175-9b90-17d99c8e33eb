import json
import uuid

import requests

# --- 配置 ---
# 请将 'YOUR_API_BASE_URL' 替换为您的API服务器地址，例如 http://127.0.0.1:8000
BASE_URL = "https://api-tokenization.sandbox.cobo.com"
# 请将 'YOUR_AUTH_TOKEN' 替换为您登录后获取的有效JWT
# 注意：'call' 和 'estimate-fee' API需要认证
AUTH_TOKEN = "YOUR_AUTH_TOKEN"

# --- 测试用的地址和参数 ---
# 一个已部署的、符合CoboERC20 ABI的合约地址
# 您需要替换为您自己的测试合约地址
ERC20_CONTRACT_ADDRESS = "0x...YourContractAddress..."
# 您希望查询余额或接收转账的钱包地址
WALLET_ADDRESS = "0x...YourWalletAddress..."
# 您希望发送代币的目标地址
RECIPIENT_ADDRESS = "0x...RecipientWalletAddress..."
# 转账金额 (以最小单位表示, e.g., 1 Token with 18 decimals)
TRANSFER_AMOUNT = 1 * (10**18)


def print_request_response(test_name, url, payload, response):
    """一个辅助函数，用于格式化打印请求和响应信息"""
    print(f"\n--- {test_name} ---")
    print(f"Request URL: POST {url}")
    print(f"Request Payload:\n{json.dumps(payload, indent=2)}")
    print(f"Response Status Code: {response.status_code}")
    try:
        print(f"Response JSON:\n{json.dumps(response.json(), indent=2)}")
    except json.JSONDecodeError:
        print(f"Response Text:\n{response.text}")
    print("-" * (len(test_name) + 6))


def test_read_api():
    """测试 /v1/contract/read/ API"""
    url = f"{BASE_URL}/v1/contract/read/"

    # 1. 测试无参数调用: 获取代币名称 (name)
    payload_name = {
        "chain_id": "ETH",
        "contract_address": ERC20_CONTRACT_ADDRESS,
        "func_name": "name",
        "func_params": [],
    }
    response_name = requests.post(url, json=payload_name)
    print_request_response("Read API (name)", url, payload_name, response_name)

    # 2. 测试有参数调用: 获取钱包余额 (balanceOf)
    payload_balance = {
        "chain_id": "ETH",
        "contract_address": ERC20_CONTRACT_ADDRESS,
        "func_name": "balanceOf",
        "func_params": [WALLET_ADDRESS],
    }
    response_balance = requests.post(url, json=payload_balance)
    print_request_response(
        "Read API (balanceOf)", url, payload_balance, response_balance
    )
    return response_balance.ok


def test_estimate_fee_api():
    """测试 /v1/contract/estimate-fee/ API"""
    url = f"{BASE_URL}/v1/contract/estimate-fee/"
    headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}

    payload = {
        "source": {
            "source_type": "ORG_WALLET",
            "wallet_id": "YOUR_ORG_WALLET_ID",  # TODO: 替换为您的组织钱包ID
        },
        "contract_address": ERC20_CONTRACT_ADDRESS,
        "data": [
            {
                "function_name": "transfer",
                "function_params": {
                    "to": RECIPIENT_ADDRESS,
                    "amount": str(TRANSFER_AMOUNT),  # 参数值建议使用字符串以避免精度问题
                },
            }
        ],
    }

    response = requests.post(url, json=payload, headers=headers)
    print_request_response("Estimate Fee API", url, payload, response)

    if response.ok:
        # 返回估算出的费用信息，以便下一步使用
        return response.json().get("result", {})
    return None


def test_call_api(estimated_fee):
    """测试 /v1/contract/call/ API"""
    if not estimated_fee:
        print("\nSkipping Call API test because fee estimation failed.")
        return

    url = f"{BASE_URL}/v1/contract/call/"
    headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}

    payload = {
        "request_id": f"sdk-test-{uuid.uuid4()}",
        "source": {
            "source_type": "ORG_WALLET",
            "wallet_id": "YOUR_ORG_WALLET_ID",  # TODO: 替换为您的组织钱包ID
        },
        "token_id": ERC20_CONTRACT_ADDRESS,
        "func_name": "multicall",
        "contract_address": ERC20_CONTRACT_ADDRESS,
        "data": [
            {
                "function_name": "transfer",
                "function_params": {
                    "to": RECIPIENT_ADDRESS,
                    "amount": str(TRANSFER_AMOUNT),
                },
            }
        ],
        "fee": estimated_fee,
    }

    response = requests.post(url, json=payload, headers=headers)
    print_request_response("Call API", url, payload, response)
    return response.ok


if __name__ == "__main__":
    print("Starting API tests for /v1/contract/ endpoints...")

    if (
        "YOUR_AUTH_TOKEN" in AUTH_TOKEN
        or "YourContractAddress" in ERC20_CONTRACT_ADDRESS
    ):
        print(
            "\n!!! WARNING: Please update the configuration variables (BASE_URL, AUTH_TOKEN, ERC20_CONTRACT_ADDRESS, etc.) at the top of the script before running. !!!"
        )
    else:
        # 按顺序执行测试
        if test_read_api():
            fee_info = test_estimate_fee_api()
            if fee_info:
                test_call_api(fee_info)

    print("\nAPI tests finished.")
