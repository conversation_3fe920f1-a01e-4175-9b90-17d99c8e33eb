#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", f"main.settings")
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc

    if len(sys.argv) > 1 and sys.argv[1] in ():
        # fix Error: maximum recursion depth exceeded(gevent and requests)
        from gevent import monkey

        monkey.patch_all(thread=False, select=False)

    from django.conf import settings

    if settings.USE_SQL_POOL:
        from cobo_libs.utils.sqlpool import patch_mysql

        patch_mysql()

    # set logger extra tag: command
    if len(sys.argv) > 1:
        from cobo_libs.utils.logger import add_logger_tag

        add_logger_tag("command", sys.argv[1])

    execute_from_command_line(sys.argv)


if __name__ == "__main__":
    main()
