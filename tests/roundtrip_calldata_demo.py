import json

from apps.utils.CoboERC20 import COBO_ERC20_ABI
from apps.utils.web3 import decode_function_input, encode_function_input


def main():
    """
    演示了从 calldata -> 解码 -> 再编码 -> calldata 的往返过程，
    并验证了原始 calldata 和再编码后的 calldata 是否一致。
    """
    print("=" * 60)
    print(" Roundtrip Calldata Demo (Decode -> Re-encode -> Verify) ")
    print("=" * 60)

    # --- 示例 1: 简单的 'transfer' 调用 ---
    original_calldata_simple = "0xa9059cbb00000000000000000000000035564858f1e007f2ea49365e285e2b82565073a50000000000000000000000000000000000000000000000000de0b6b3a7640000"

    print("\n--- 1. Simple 'transfer' Roundtrip Demo ---")
    print(f"Original calldata:\n  {original_calldata_simple}")

    # 1. 解码
    print("\n[Step 1: Decoding]")
    decoded_data = decode_function_input(COBO_ERC20_ABI, original_calldata_simple)
    if not decoded_data:
        print("❌ Decoding failed.")
        return

    func_obj, func_params = decoded_data
    print("✅ Decoded Data:")
    print(f"   Function: {func_obj.fn_name}")
    print(f"   Params: {json.dumps(func_params, indent=4)}")

    # 2. 重新编码
    print("\n[Step 2: Re-encoding]")
    re_encoded_calldata = encode_function_input(
        COBO_ERC20_ABI, func_obj.fn_name, func_params
    )
    print(f"✅ Re-encoded calldata:\n  {re_encoded_calldata}")

    # 3. 验证
    print("\n[Step 3: Verifying]")
    if original_calldata_simple == re_encoded_calldata:
        print("✅ SUCCESS: Original and re-encoded calldata match!")
    else:
        print("❌ FAILURE: Calldata does not match!")

    # --- 示例 2: 复杂的 'multicall' 调用 ---
    original_calldata_multicall = "0xac9650d800000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000004440c10f190000000000000000000000008e67e09103c91df30e56d7967d06034f18d13c80000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004440c10f190000000000000000000000008e67e09103c91df30e56d7967d06034f18d13c80000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000"
    print("\n\n--- 2. 'multicall' Roundtrip Demo ---")
    print(f"Original calldata:\n  {original_calldata_multicall[:100]}...")

    # 1. 解码
    print("\n[Step 1: Decoding]")
    decoded_data_multi = decode_function_input(
        COBO_ERC20_ABI, original_calldata_multicall
    )
    if not decoded_data_multi:
        print("❌ Decoding failed.")
        return

    func_obj_multi, func_params_multi = decoded_data_multi
    print("✅ Decoded Data:")
    print(f"   Function: {func_obj_multi.fn_name}")
    print(f"   Params: {json.dumps(func_params_multi, indent=4)}")

    # 2. 重新编码
    print("\n[Step 2: Re-encoding]")
    re_encoded_calldata_multi = encode_function_input(
        COBO_ERC20_ABI, func_obj_multi.fn_name, func_params_multi
    )
    print(f"✅ Re-encoded calldata:\n  {re_encoded_calldata_multi[:100]}...")

    # 3. 验证
    print("\n[Step 3: Verifying]")
    if original_calldata_multicall == re_encoded_calldata_multi:
        print("✅ SUCCESS: Original and re-encoded multicall calldata match!")
    else:
        print("❌ FAILURE: Calldata does not match!")


if __name__ == "__main__":
    main()
