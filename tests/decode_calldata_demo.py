import json

from apps.utils.CoboERC20 import COBO_ERC20_ABI
from apps.utils.web3 import decode_function_input

# 这是一个调用 multicall(bytes[]) 函数的示例 calldata
# 它内部打包了两个对 mint(address,uint256) 的调用
sample_call_data = "0xac9650d800000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000442f2ff15d65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a0000000000000000000000008e67e09103c91df30e56d7967d06034f18d13c800000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000442f2ff15d3c11d16cbaffd01df69ce1c404f6340ee057498f5f00246190ea54220576a8480000000000000000000000008e67e09103c91df30e56d7967d06034f18d13c8000000000000000000000000000000000000000000000000000000000"


def main():
    """主函数"""
    print("--- 统一的 call_data 解码函数 (自动处理 multicall) 示例 ---")
    print(f"待解码的 multicall data: {sample_call_data[:100]}...\n")

    # 直接调用增强后的解码函数
    decoded_data = decode_function_input(COBO_ERC20_ABI, sample_call_data)
    print(decoded_data)

    if decoded_data:
        func_obj, func_params = decoded_data
        print("✅ 解码成功!")
        print(f"顶层函数: {func_obj.fn_name}\n")

        # 使用 json.dumps 进行格式化输出，以更好地显示嵌套结构
        # 我们将地址等非JSON序列化类型转换为字符串
        def json_converter(o):
            if isinstance(o, bytes):
                return o.hex()
            raise TypeError(
                f"Object of type {o.__class__.__name__} is not JSON serializable"
            )

        print("解码后的参数详情:")
        print(json.dumps(func_params, indent=2, default=json_converter))

    else:
        print("❌ 解码失败。")


if __name__ == "__main__":
    main()
