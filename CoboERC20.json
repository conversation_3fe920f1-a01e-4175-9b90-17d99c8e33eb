{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "BURNER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MANAGER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MINTER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "PAUSER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SALVAGER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "UPGRADE_INTERFACE_VERSION", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "accessListAdd", "inputs": [{"name": "accounts", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "accessListEnabled", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "accessListRemove", "inputs": [{"name": "accounts", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "blockListAdd", "inputs": [{"name": "accounts", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "blockListRemove", "inputs": [{"name": "accounts", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burn", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burnFrom", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "contractUri", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "contractUriUpdate", "inputs": [{"name": "_uri", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "getAccessList", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getBlockList", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMember", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMemberCount", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getRoleMembers", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_name", "type": "string", "internalType": "string"}, {"name": "_symbol", "type": "string", "internalType": "string"}, {"name": "_uri", "type": "string", "internalType": "string"}, {"name": "admin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "isAccessListed", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isBlockListed", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "multicall", "inputs": [{"name": "data", "type": "bytes[]", "internalType": "bytes[]"}], "outputs": [{"name": "results", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "proxiableUUID", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "salvageERC20", "inputs": [{"name": "token", "type": "address", "internalType": "contract IERC20"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "salvageNative", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "toggleAccesslist", "inputs": [{"name": "enabled", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "upgradeToAndCall", "inputs": [{"name": "newImplementation", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "payable"}, {"type": "function", "name": "version", "inputs": [], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "event", "name": "AccessListAddressAdded", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AccessListAddressRemoved", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AccesslistToggled", "inputs": [{"name": "enabled", "type": "bool", "indexed": true, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BlockListAddressAdded", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "BlockListAddressRemoved", "inputs": [{"name": "account", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "ContractUriUpdated", "inputs": [{"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "old<PERSON><PERSON>", "type": "string", "indexed": false, "internalType": "string"}, {"name": "newUri", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "NativeSalvaged", "inputs": [{"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TokenSalvaged", "inputs": [{"name": "caller", "type": "address", "indexed": true, "internalType": "address"}, {"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Upgraded", "inputs": [{"name": "implementation", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BlockedAddress", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "DefaultAdminError", "inputs": []}, {"type": "error", "name": "ERC1967InvalidImplementation", "inputs": [{"name": "implementation", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC1967Non<PERSON>ayable", "inputs": []}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "FailedCall", "inputs": []}, {"type": "error", "name": "InvalidAddress", "inputs": []}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotAccessListAddress", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "SalvageNativeFailed", "inputs": []}, {"type": "error", "name": "UUPSUnauthorizedCallContext", "inputs": []}, {"type": "error", "name": "UUPSUnsupportedProxiableUUID", "inputs": [{"name": "slot", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ZeroAmount", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "1300:12962:57:-:0;;;;;;;1171:4:23;1163:13;;3147:66:22;1300:12962:57;;;;;;;;;7894:76:22;;-1:-1:-1;;;;;;;;;;;;1300:12962:57;;;7983:34:22;7979:146;;-1:-1:-1;1300:12962:57;;;;;;;;1163:13:23;1300:12962:57;;;;;;;;;;;7979:146:22;-1:-1:-1;;;;;;1300:12962:57;;;;;;;;;;;;;8085:29:22;;1300:12962:57;;8085:29:22;7979:146;;;;;7894:76;-1:-1:-1;;;7936:23:22;;;;;1300:12962:57;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1300:12962:57:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;1908:26;1300:12962;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;2921:11:63;;2917:56;;3283:4:20;;:::i;:::-;1300:12962:57;966:10:25;;;;;3018:36:63;;;;:::i;:::-;;3062:8;3058:62;;966:10:25;3128:36:63;1300:12962:57;3128:36:63;;1300:12962:57;3058:62:63;1300:12962:57;;;3084:31:63;;;;2917:56;1300:12962:57;;;2946:22:63;;;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;10387:55:54;1300:12962:57;;;:::i;:::-;;-1:-1:-1;1300:12962:57;5006:14:54;1300:12962:57;;;-1:-1:-1;1300:12962:57;;5006:26:54;;4910:129;;10387:55;1300:12962:57;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;2608:25;1300:12962;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;2199:24;1300:12962;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;4771:20:24;1300:12962:57;;:::i;:::-;4771:20:24;1300:12962:57;;;;4771:13:24;1300:12962:57;;;;;;;4771:20:24;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;:::i;:::-;2099:26:62;;:53;;;1300:12962:57;2095:120:62;;1300:12962:57;5815:26:20;1300:12962:57;;;2968:71:20;1300:12962:57;;3283:4:20;1300:12962:57;;;;4967:24:20;1300:12962:57;3283:4:20;:::i;:::-;5815:26;:::i;:::-;1300:12962:57;2095:120:62;1300:12962:57;;;2175:29:62;;;;2099:53;966:10:25;;1300:12962:57;;;2129:23:62;2099:53;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;2916:24;1300:12962;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;1451:81:21;1300:12962:57;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;2339:11:63;;;2335:56;;3283:4:20;;:::i;:::-;1300:12962:57;;;1328:43:43;;;1300:12962:57;1328:43:43;;966:10:25;1300:12962:57;1328:43:43;;1300:12962:57;;;;;;;1328:43:43;;1300:12962:57;;;;;;;;;;;;;;;;;;;;8507:421:43;;;;;;;1300:12962:57;8507:421:43;;8942:15;;8960:26;;;:31;8942:68;8938:146;;966:10:25;2466:51:63;1300:12962:57;2466:51:63;;1300:12962:57;8938:146:43;1300:12962:57;;;;9033:40:43;;;;1300:12962:57;9033:40:43;;1300:12962:57;9033:40:43;8942:68;1300:12962:57;8994:16:43;;8942:68;;8507:421;1300:12962:57;8507:421:43;;1300:12962:57;8507:421:43;;;;;1300:12962:57;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;9363:10:59;1300:12962:57;;;;;;;;;;9363:10:59;1300:12962:57;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;:::i;:::-;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;;;;;-1:-1:-1;;1300:12962:57;;;;;;10387:55:54;1300:12962:57;;;:::i;:::-;;-1:-1:-1;1300:12962:57;5006:14:54;1300:12962:57;;;-1:-1:-1;1300:12962:57;;5006:26:54;;4910:129;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;1822:13:26;;;1300:12962:57;1837:15:26;;;;;;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1643:26:26;1300:12962:57;;;;;;;;:::i;:::-;;;;;;;;;;;;;;1854:3:26;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1643:26:26;1300:12962:57;;;;4107:55:44;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;4065:25:44;1923:4:26;;4065:25:44;;;;:::i;:::-;1923:4:26;;4107:55:44;:::i;:::-;1873:88:26;;;;:::i;:::-;;;;;;:::i;:::-;;1300:12962:57;1822:13:26;;1300:12962:57;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;4545:5:24;1300:12962:57;;:::i;:::-;1944:72:27;;:::i;:::-;8225:12:57;966:10:25;8225:12:57;:::i;:::-;8263:2;;;:::i;:::-;1300:12962;;966:10:25;;4545:5:24;:::i;:::-;1300:12962:57;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;:::i;:::-;3283:4:20;;:::i;:::-;1300:12962:57;6754:10:59;;;;;;1300:12962:57;6766:3:59;1300:12962:57;6780:11:59;;;;;;;;:::i;:::-;;:::i;:::-;1300:12962:57;6780:25:59;6776:76;;6875:11;9425:50:54;1300:12962:57;6875:11:59;;;;;;;:::i;:::-;1300:12962:57;9425:50:54;:::i;:::-;6856:84:59;;6766:3;;1300:12962:57;6739:13:59;;6856:84;6922:11;;;;;;:::i;:::-;1300:12962:57;6900:34:59;1300:12962:57;6900:34:59;;6856:84;;;6776:76;1300:12962:57;;;6820:26:59;;;;1300:12962:57;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;1451:81:21;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;3086:9:24;1300:12962:57;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;;;;2968:71:20;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;1451:81:21;1300:12962:57;;;;5662:18:54;1300:12962:57;;;;;5662:18:54;:::i;:::-;1300:12962:57;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;8271:11:59;1300:12962:57;;;;;;;;;;8271:11:59;1300:12962:57;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;966:10:25;1300:12962:57;;;;;;;;;;;2199:24;;1300:12962;;4516:23:20;4512:108;;1944:72:27;;:::i;:::-;1237:66;3300:4;1300:12962:57;;;;;;;3319:20:27;1300:12962:57;;;966:10:25;1300:12962:57;;3319:20:27;1300:12962:57;4512:108:20;1300:12962:57;;;;4562:47:20;;;;966:10:25;1300:12962:57;4562:47:20;;1300:12962:57;;;;;4562:47:20;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;2008:24:60;1300:12962:57;;;;;;:::i;:::-;3283:4:20;;:::i;:::-;1300:12962:57;;;:::i;:::-;2008:24:60;:::i;1300:12962:57:-;;;;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;;3283:4:20;;:::i;:::-;7146:11:57;;7142:46;;7213:6;;;:::i;1300:12962::-;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;:::i;:::-;;;;2031:63:24;1300:12962:57;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;1237:66:27;1300:12962:57;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;3147:66:22;1300:12962:57;;;;;4724:16:22;;:34;;;;1300:12962:57;4803:1:22;4788:16;:50;;;;1300:12962:57;4853:13:22;:30;;;;1300:12962:57;4849:91:22;;;4803:1;1300:12962:57;;;;3147:66:22;1300:12962:57;;;;;;4301:16:22;4977:67;;1300:12962:57;;;;;4392:19;4388:83;;1300:12962;6891:76:22;1300:12962:57;6891:76:22;;;:::i;1300:12962:57:-;;;;;:::i;:::-;6891:76:22;;;:::i;:::-;;;:::i;:::-;1300:12962:57;;;;;;;;;;;2581:7:24;1300:12962:57;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;4803:1:22;1300:12962:57;;2581:7:24;1300:12962:57;;;;;2581:7:24;1300:12962:57;;;;;;;;;;2606:9:24;1300:12962:57;;:::i;:::-;;;;;;;;;;;;;;;;;;1622:4:60;1300:12962:57;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;4803:1:22;1300:12962:57;;2581:7:24;1300:12962:57;;;;;2606:9:24;1300:12962:57;;6891:76:22;;:::i;:::-;;;:::i;1300:12962:57:-;6891:76:22;;:::i;1622:4:60:-;6891:76:22;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;;;:::i;:::-;1300:12962:57;3340:25:59;1300:12962:57;;3340:25:59;1300:12962:57;4241:31:21;1300:12962:57;;4241:31:21;:::i;:::-;4282:71;;1300:12962:57;;;;4301:16:22;5064:101;;1300:12962:57;5064:101:22;1300:12962:57;3147:66:22;1300:12962:57;;3147:66:22;1300:12962:57;5140:14:22;1300:12962:57;;;4803:1:22;1300:12962:57;;5140:14:22;1300:12962:57;4282:71:21;1300:12962:57;;;1451:81:21;1300:12962:57;;9425:50:54;1300:12962:57;;;;;9425:50:54;:::i;:::-;;4282:71:21;;1300:12962:57;;;;-1:-1:-1;1300:12962:57;;;;;;;;;2606:9:24;1300:12962:57;;;;;;;;;;;;;;;;;;4803:1:22;1300:12962:57;;1622:4:60;1300:12962:57;;;;;;;;;;;;2606:9:24;1300:12962:57;;;;;;-1:-1:-1;;1300:12962:57;;2581:7:24;1300:12962:57;;;;;;;;;;;;;;;;;;;4803:1:22;1300:12962:57;;;;;;;;;;;;;2606:9:24;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4803:1:22;1300:12962:57;;;;;;-1:-1:-1;1300:12962:57;;;;;;-1:-1:-1;1300:12962:57;;;;;;;2581:7:24;1300:12962:57;;;;;;;;;;;;;;4803:1:22;1300:12962:57;;;;;;;;;;;;;;;;2581:7:24;1300:12962:57;;;;;;-1:-1:-1;;1300:12962:57;;2581:7:24;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;4803:1:22;1300:12962:57;;;;;;;;;;;;;;2581:7:24;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4803:1:22;1300:12962:57;;;;-1:-1:-1;1300:12962:57;;;4977:67:22;1300:12962:57;;;;;3147:66:22;1300:12962:57;4977:67:22;;4849:91;1300:12962:57;;;4906:23:22;;;;4853:30;4870:13;;;4853:30;;;4788:50;4816:4;4808:25;:30;;-1:-1:-1;4788:50:22;;4724:34;1300:12962:57;;;;;;4301:16:22;;-1:-1:-1;4724:34:22;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;3147:66:22;1300:12962:57;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;5090:6:23;1300:12962:57;5081:4:23;5073:23;5069:145;;1300:12962:57;;;811:66:38;1300:12962:57;;;5069:145:23;1300:12962:57;;;5174:29:23;;;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;:::i;:::-;3283:4:20;;;:::i;:::-;1300:12962:57;7643:10:59;;;;;;1300:12962:57;7655:3:59;1300:12962:57;;;9746:53:54;7687:11:59;;;;;;;:::i;:::-;1300:12962:57;9746:53:54;:::i;:::-;7665:89:59;;7655:3;;1300:12962:57;7628:13:59;;7665:89;7736:11;;;;;;:::i;:::-;1300:12962:57;7712:36:59;1300:12962:57;7712:36:59;;7665:89;;;1300:12962:57;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4667:6:23;;;1300:12962:57;4658:4:23;;4650:23;:120;;;;;1300:12962:57;4633:251:23;;;966:10:25;1300:12962:57;;;;;;;;;;;;;;;;;1908:26;;1300:12962;;4516:23:20;4512:108;;1300:12962:57;;;;;;;6131:52:23;;;;1300:12962:57;6131:52:23;;;;1300:12962:57;;6131:52:23;;;1300:12962:57;-1:-1:-1;6127:437:23;;1300:12962:57;;;;6493:60:23;;;;1300:12962:57;6493:60:23;;1300:12962:57;6493:60:23;6127:437;811:66:38;;;;6225:40:23;;;;6221:120;;1748:29:38;;;:34;1744:119;;1300:12962:57;;;;;;;;;;;2407:36:38;;1300:12962:57;2407:36:38;;1300:12962:57;;2458:15:38;:11;;4065:25:44;1300:12962:57;4065:25:44;;;4107:55;4065:25;;;;;;;;;:::i;:::-;4107:55;;:::i;2454:148:38:-;6163:9;;;;;6159:70;;1300:12962:57;6159:70:38;6199:19;;1300:12962:57;6199:19:38;;;1744:119;1300:12962:57;;;1805:47:38;;;;1300:12962:57;1805:47:38;;1300:12962:57;1805:47:38;6221:120:23;1300:12962:57;;6292:34:23;;;;1300:12962:57;6292:34:23;;1300:12962:57;6292:34:23;6131:52;;;;;;;;;;;;;;;;;:::i;:::-;;;1300:12962:57;;;;;6131:52:23;;;;;;;;;4512:108:20;1300:12962:57;;;;;4562:47:20;;;;966:10:25;1300:12962:57;4562:47:20;;1300:12962:57;;;;4562:47:20;4650:120:23;1300:12962:57;;;811:66:38;1300:12962:57;;4728:42:23;;4650:120;;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;756:29:59;1300:12962:57;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;3283:4:20;;:::i;:::-;1300:12962:57;3940:27:59;1300:12962:57;;;;;;3940:27:59;1300:12962:57;3982:26:59;1300:12962:57;3982:26:59;;1300:12962:57;;;;;;-1:-1:-1;;1300:12962:57;;;;;966:10:25;1300:12962:57;;;;;;;;;;;;;;3221:24;;1300:12962;;4516:23:20;4512:108;;6279:11:57;;;6275:46;;6351:6;966:10:25;;6351:6:57;:::i;1300:12962::-;;;;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;;1944:72:27;;;:::i;:::-;966:10:25;1300:12962:57;;;;;;;;;;;2916:24;;1300:12962;;4516:23:20;4512:108;;5526:11:57;;;5522:46;;5593:2;;1300:12962;5593:2;;:::i;:::-;1300:12962;8707:21:24;;;8703:91;;7402:14;1300:12962:57;;;;;;;;;;;;;8262:25:24;1300:12962:57;;;;;;;2031:63:24;1300:12962:57;;;;;;;;;;;;;;;;8262:25:24;1300:12962:57;;;;;;;;;;;8703:91:24;1300:12962:57;;;8751:32:24;;;1300:12962:57;;8751:32:24;;1300:12962:57;8751:32:24;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;3283:4:20;;:::i;:::-;1237:66:27;1300:12962:57;;;;;2971:9:27;2967:62;;1300:12962:57;;;;3627:22:27;1300:12962:57;;;966:10:25;1300:12962:57;;3627:22:27;1300:12962:57;2967:62:27;1300:12962:57;;;3003:15:27;;;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;:::i;:::-;3283:4:20;;:::i;:::-;1300:12962:57;4899:10:59;;;;;;1300:12962:57;4911:3:59;1300:12962:57;4925:11:59;;;;;;;;:::i;:::-;1300:12962:57;4925:25:59;4921:76;;5021:11;9425:50:54;1300:12962:57;5021:11:59;;;;;;;:::i;:::-;1300:12962:57;9425:50:54;:::i;:::-;5001:86:59;;4911:3;;1300:12962:57;4884:13:59;;5001:86;5069:11;;;;;;:::i;:::-;1300:12962:57;5046:35:59;1300:12962:57;5046:35:59;;5001:86;;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;:::i;:::-;2834:26:62;;:73;;;1300:12962:57;2830:140:62;;966:10:25;1300:12962:57;;;6489:34:20;6485:102;;6597:37;;;:::i;6485:102::-;1300:12962:57;;;6546:30:20;;;;2834:73:62;-1:-1:-1;1300:12962:57;;;1451:81:21;1300:12962:57;;;;;2864:43:62;2834:73;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;3808:2:24;1300:12962:57;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;:::i;:::-;;;;2968:71:20;1300:12962:57;;3283:4:20;1300:12962:57;;;;4967:24:20;1300:12962:57;3283:4:20;:::i;:::-;4241:31:21;;;;:::i;:::-;4282:71;;1300:12962:57;4282:71:21;9425:50:54;1300:12962:57;;;1451:81:21;1300:12962:57;;;;;;;;9425:50:54;;:::i;1300:12962:57:-;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;:::i;:::-;3283:4:20;;;:::i;:::-;1300:12962:57;5797:10:59;;;;;;1300:12962:57;5809:3:59;1300:12962:57;;;9746:53:54;5842:11:59;;;;;;;:::i;:::-;1300:12962:57;9746:53:54;:::i;:::-;5819:91:59;;5809:3;;1300:12962:57;5782:13:59;;5819:91;5892:11;;;;;;:::i;:::-;1300:12962:57;5867:37:59;1300:12962:57;5867:37:59;;5819:91;;;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;3221:24;1300:12962;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;2968:71:20;1300:12962:57;;;;;;;4967:24:20;1300:12962:57;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;;:::i;:::-;;;1944:72:27;;;:::i;:::-;9766:12:57;966:10:25;9766:12:57;:::i;:::-;9804:4;;;:::i;:::-;9834:2;;;:::i;:::-;4771:20:24;;1300:12962:57;;;;4771:13:24;1300:12962:57;;;;;;;4771:20:24;966:10:25;1300:12962:57;;;;;;;;11814:36:24;-1:-1:-1;;11814:36:24;;11810:309;;1300:12962:57;6102:5:24;;;;:::i;11810:309::-;11870:24;;;11866:130;;1300:12962:57;;;11045:19:24;11041:89;;966:10:25;11143:21:24;11139:90;;11238:20;6102:5;11238:20;;;1300:12962:57;;;;4771:13:24;1300:12962:57;;;;;;;11238:20:24;966:10:25;1300:12962:57;;;;;;;;;11810:309:24;;11139:90;1300:12962:57;;;11187:31:24;;;1300:12962:57;;11187:31:24;;1300:12962:57;11187:31:24;11041:89;1300:12962:57;;;11087:32:24;;;1300:12962:57;;11087:32:24;;1300:12962:57;11087:32:24;11866:130;1300:12962:57;;11921:60:24;;;966:10:25;1300:12962:57;11921:60:24;;1300:12962:57;;;;;;;;;;;;;;;11921:60:24;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;3981:14:24;1300:12962:57;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;:::i;:::-;;;966:10:25;;11045:19:24;11041:89;;1300:12962:57;;11143:21:24;;;11139:90;;966:10:25;1300:12962:57;;;;4771:13:24;1300:12962:57;;;;;;;;;;;;;;;;;;;;11319:31:24;1300:12962:57;966:10:25;11319:31:24;;1300:12962:57;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;2827:7:24;1300:12962:57;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1300:12962:57;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;3549:26;1300:12962;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;1851:57:21;1866:42;1300:12962:57;1851:57:21;;:97;;;;;1300:12962:57;;;;;;;1851:97:21;3614:32:20;3599:47;;;-1:-1:-1;3599:87:20;;;;1851:97:21;;;;;3599:87:20;1116:25:28;1101:40;;;3599:87:20;;;1300:12962:57;;;;;;;;-1:-1:-1;;1300:12962:57;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;:::o;:::-;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;-1:-1:-1;1300:12962:57;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;753:25:60;1300:12962:57;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;753:25:60;-1:-1:-1;1300:12962:57;;;-1:-1:-1;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;-1:-1:-1;1300:12962:57;;;;:::o;:::-;;;:::o;2709:128:27:-;1300:12962:57;1237:66:27;1300:12962:57;;2770:61:27;;2709:128::o;2770:61::-;2805:15;1300:12962:57;;2805:15:27;;;;13966:294:57;1300:12962;14043:17;1300:12962;;14039:130;;13966:294;1300:12962;;10387:55:54;;-1:-1:-1;1300:12962:57;5006:14:54;1300:12962:57;;;-1:-1:-1;1300:12962:57;;5006:26:54;;4910:129;;10387:55;14179:74:57;;13966:294;:::o;14179:74::-;1300:12962;;;;14220:33;;;;;;;1300:12962;14220:33;14039:130;1300:12962;;;10387:55:54;;-1:-1:-1;1300:12962:57;5006:14:54;1300:12962:57;;;-1:-1:-1;1300:12962:57;;5006:26:54;;4910:129;;10387:55;14080:30:57;14076:82;;14039:130;;;14076:82;1300:12962;;;;14119:39;;;;;;;1300:12962;14119:39;4196:103:20;966:10:25;-1:-1:-1;1300:12962:57;;;;;;;;;;2608:25;;1300:12962;;4516:23:20;4512:108;;4196:103;:::o;:::-;966:10:25;-1:-1:-1;1300:12962:57;;;;;;;;;;3549:26;;1300:12962;;4516:23:20;4512:108;;4196:103;:::o;:::-;1300:12962:57;-1:-1:-1;1300:12962:57;2968:71:20;1300:12962:57;;;-1:-1:-1;1300:12962:57;966:10:25;-1:-1:-1;1300:12962:57;;;;;-1:-1:-1;1300:12962:57;;;4516:23:20;4512:108;;4196:103;:::o;9163:206:24:-;;;1300:12962:57;;;9233:21:24;;;9229:89;;1300:12962:57;7262:546:24;1300:12962:57;2031:63:24;1300:12962:57;;;;7262:546:24;1300:12962:57;;7513:19:24;;;;7509:115;;1300:12962:57;;8262:25:24;1300:12962:57;7262:546:24;1300:12962:57;;;;;;;;;;;;;;7985:14:24;1300:12962:57;;;;;;;;;;;8262:25:24;9163:206::o;7509:115::-;1300:12962:57;;7559:50:24;;;1300:12962:57;;;;;7559:50:24;;;1300:12962:57;;;;;;;;;;;;;;;11921:60:24;9229:89;1300:12962:57;;;9277:30:24;;;9252:1;9277:30;;;1300:12962:57;9277:30:24;2310:151:60;;1300:12962:57;;;;;;;;;2379:18:60;1300:12962:57;;:::i;:::-;;;;;;2310:151:60;-1:-1:-1;1300:12962:57;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;2379:18:60;1300:12962:57;;2406:51:60;1300:12962:57;;;;;;;;;;;;:::i;:::-;;;;;;;;966:10:25;1300:12962:57;;:::i;:::-;2406:51:60;;;2310:151::o;1300:12962:57:-;;;;;;;;;;;;;;2379:18:60;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;2379:18:60;1300:12962:57;;;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;2379:18:60;-1:-1:-1;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;1300:12962:57;;;;6509:300:24;;1300:12962:57;;;;6592:18:24;;;6588:86;;1300:12962:57;6687:16:24;;;6683:86;;7262:546;1300:12962:57;;;;2031:63:24;1300:12962:57;;;;;;;7513:19:24;;;;7509:115;;1300:12962:57;;;8262:25:24;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8262:25:24;6509:300::o;7509:115::-;1300:12962:57;;7559:50:24;;;1300:12962:57;;;;;7559:50:24;;;1300:12962:57;;;;;;;;-1:-1:-1;1300:12962:57;;;;;;;;11921:60:24;1300:12962:57;5823:11:59;1300:12962:57;;;;;;5823:11:59;-1:-1:-1;1300:12962:57;;;;-1:-1:-1;1300:12962:57;:::o;:::-;7669:10:59;1300:12962:57;;;;;;7669:10:59;-1:-1:-1;1300:12962:57;;;;-1:-1:-1;1300:12962:57;:::o;:::-;;;;;;;;-1:-1:-1;1300:12962:57;;-1:-1:-1;1300:12962:57;;;-1:-1:-1;1300:12962:57;:::o;2910:1368:54:-;-1:-1:-1;1300:12962:57;;;3105:14:54;1300:12962:57;;;;;;-1:-1:-1;;1300:12962:57;3141:13:54;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;5823:11:59;1300:12962:57;;;;;;;;;;3616:23:54;;;3612:378;;3137:1135;1300:12962:57;;;5823:11:59;1300:12962:57;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;5823:11:59;1300:12962:57;;;3105:14:54;1300:12962:57;;;;;;3105:14:54;4207:11;:::o;1300:12962:57:-;;;;;;;;;;3612:378:54;1300:12962:57;3679:22:54;3800:23;3679:22;;:::i;:::-;1300:12962:57;;;;;;3800:23:54;;;;:::i;:::-;1300:12962:57;;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;3105:14:54;1300:12962:57;;;;;;3612:378:54;;;;;1300:12962:57;;;;;;;;;;;;;;;;;;;;3137:1135:54;4249:12;;;:::o;2910:1368::-;-1:-1:-1;1300:12962:57;;;3105:14:54;1300:12962:57;;;;;;-1:-1:-1;;1300:12962:57;3141:13:54;;;;-1:-1:-1;;1300:12962:57;;;;;;;;;7669:10:59;1300:12962:57;;;;;;;;;;3616:23:54;;;3612:378;;3137:1135;1300:12962:57;;;7669:10:59;1300:12962:57;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;7669:10:59;1300:12962:57;;;3105:14:54;1300:12962:57;;;;;;3105:14:54;4207:11;:::o;3612:378::-;1300:12962:57;3679:22:54;3800:23;3679:22;;:::i;:::-;1300:12962:57;;;;;;3800:23:54;;;;:::i;1300:12962:57:-;;;;;3105:14:54;1300:12962:57;;;;;;3612:378:54;;;;;2910:1368;;3105:14;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;3141:13:54;;;;3137:1135;3141:13;;;-1:-1:-1;;1300:12962:57;;;;;;;;;;;;;;;;;;;;3616:23:54;;;3612:378;;3137:1135;1300:12962:57;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;3105:14:54;4207:11;:::o;1300:12962:57:-;;;;;;;;;;3612:378:54;1300:12962:57;3679:22:54;3800:23;3679:22;;;:::i;:::-;1300:12962:57;;;;;;3800:23:54;;;;;:::i;1300:12962:57:-;;;;;;;;;;;;3612:378:54;;;;;1300:12962:57;;;;;;;;;;;;;;;;;;;;3137:1135:54;4249:12;;;;;:::o;7318:387:20:-;1300:12962:57;;;;;;;;;;;;;;;;2968:71:20;;1300:12962:57;;;;;;;;;;;;;;;;;;;;7557:4:20;1300:12962:57;;;;;;;966:10:25;7580:40:20;;;;;7557:4;7634:11;:::o;7318:387::-;;-1:-1:-1;1300:12962:57;;;;2968:71:20;1300:12962:57;;;;;;;;;;;;;;;;;;;;;7484:23:20;7480:219;1300:12962:57;;;;;;;;;;;;;;;;;;;7557:4:20;1300:12962:57;;;;;;;7580:40:20;966:10:25;7580:40:20;;;7557:4;7634:11;:::o;7480:219::-;7676:12;;;;:::o;4486:353:21:-;4693:32;;;;:::i;:::-;4735:74;;;;4818:14;;4486:353;:::o;4735:74::-;9746:53:54;1300:12962:57;-1:-1:-1;1300:12962:57;1451:81:21;1300:12962:57;;;;-1:-1:-1;1300:12962:57;;;9746:53:54;;:::i;:::-;;4486:353:21;:::o;2336:406:54:-;-1:-1:-1;1300:12962:57;;;5006:14:54;1300:12962:57;;;;;;;;5005:11:59;1300:12962:57;;;;;;;;;;;;5006:14:54;1300:12962:57;;;5005:11:59;1300:12962:57;;:::i;:::-;;;5005:11:59;1300:12962:57;;;;5006:14:54;1300:12962:57;;;;5006:14:54;2671:11;:::o;1300:12962:57:-;;;;;;;;;;2415:321:54;2713:12;;;:::o;2336:406::-;-1:-1:-1;1300:12962:57;;;5006:14:54;1300:12962:57;;;;;;;;6860:10:59;1300:12962:57;;;;;;;;;;;;5006:14:54;1300:12962:57;;;6860:10:59;1300:12962:57;;:::i;:::-;;;6860:10:59;1300:12962:57;;;;5006:14:54;1300:12962:57;;;;5006:14:54;2671:11;:::o;2336:406::-;;;5006:14;;;-1:-1:-1;1300:12962:57;;;;;;;;;;;5006:26:54;2415:321;1300:12962:57;;;;;;;;;;;;;;;;5006:14:54;1300:12962:57;;;;;;;;;:::i;:::-;;;;;;;;;;;5006:14:54;2671:11;:::o;1300:12962:57:-;;;;;;;;;;2415:321:54;-1:-1:-1;2713:12:54;-1:-1:-1;;2713:12:54:o;7082:141:22:-;1300:12962:57;3147:66:22;1300:12962:57;;;;7148:18:22;7144:73;;7082:141::o;7144:73::-;7189:17;1300:12962:57;;7189:17:22;;;;4437:582:44;;4609:8;;-1:-1:-1;1300:12962:57;;5690:21:44;:17;;5815:158;;;;;;5686:354;6010:19;1300:12962:57;;6010:19:44;;;;4605:408;1300:12962:57;;4857:22:44;:49;;;4605:408;4853:119;;4985:17;;:::o;4853:119::-;1300:12962:57;;;;;4933:24:44;;;;1300:12962:57;4933:24:44;;;1300:12962:57;4933:24:44;4857:49;4883:18;;;:23;4857:49;;7942:388:20;;-1:-1:-1;1300:12962:57;;;;2968:71:20;1300:12962:57;;;;;;;;;;;;;;;;;;;;;8105:219:20;1300:12962:57;;;;;;;;;;;;;;;;;;;;;;;;;8205:40:20;966:10:25;8205:40:20;;;1300:12962:57;8259:11:20;:::o", "linkReferences": {}, "immutableReferences": {"40320": [{"start": 6722, "length": 32}, {"start": 7127, "length": 32}]}}, "methodIdentifiers": {"BURNER_ROLE()": "282c51f3", "DEFAULT_ADMIN_ROLE()": "a217fddf", "MANAGER_ROLE()": "ec87621c", "MINTER_ROLE()": "d5391393", "PAUSER_ROLE()": "e63ab1e9", "SALVAGER_ROLE()": "06e92059", "UPGRADER_ROLE()": "f72c0d8b", "UPGRADE_INTERFACE_VERSION()": "ad3cb1cc", "accessListAdd(address[])": "3e26fb1c", "accessListEnabled()": "4dc12120", "accessListRemove(address[])": "2eca959e", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "blockListAdd(address[])": "a4c20ef6", "blockListRemove(address[])": "524da02a", "burn(uint256)": "42966c68", "burnFrom(address,uint256)": "79cc6790", "contractUri()": "c0e24d5e", "contractUriUpdate(string)": "7ffc5a5c", "decimals()": "313ce567", "getAccessList()": "8d5ddbb3", "getBlockList()": "c2ee0a57", "getRoleAdmin(bytes32)": "248a9ca3", "getRoleMember(bytes32,uint256)": "9010d07c", "getRoleMemberCount(bytes32)": "ca15c873", "getRoleMembers(bytes32)": "a3246ad3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "initialize(string,string,string,address)": "5c6d8da1", "isAccessListed(address)": "f0dc51f6", "isBlockListed(address)": "b2c1e0de", "mint(address,uint256)": "40c10f19", "multicall(bytes[])": "ac9650d8", "name()": "06fdde03", "pause()": "8456cb59", "paused()": "5c975abb", "proxiableUUID()": "52d1902d", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "salvageERC20(address,uint256)": "c3d00d4e", "salvageNative(uint256)": "f18e8a0b", "supportsInterface(bytes4)": "01ffc9a7", "symbol()": "95d89b41", "toggleAccesslist(bool)": "479f0294", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "unpause()": "3f4ba83a", "upgradeToAndCall(address,bytes)": "4f1ef286", "version()": "54fd4d50"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.23+commit.f704f362\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"target\",\"type\":\"address\"}],\"name\":\"AddressEmptyCode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"BlockedAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DefaultAdminError\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"ERC1967InvalidImplementation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ERC1967NonPayable\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidInitialization\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"NotAccessListAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotInitializing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SalvageNativeFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UUPSUnauthorizedCallContext\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"UUPSUnsupportedProxiableUUID\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAmount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AccessListAddressAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AccessListAddressRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bool\",\"name\":\"enabled\",\"type\":\"bool\"}],\"name\":\"AccesslistToggled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"BlockListAddressAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"BlockListAddressRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"oldUri\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"newUri\",\"type\":\"string\"}],\"name\":\"ContractUriUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"version\",\"type\":\"uint64\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"NativeSalvaged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"caller\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TokenSalvaged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"BURNER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MANAGER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MINTER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PAUSER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SALVAGER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"UPGRADER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"UPGRADE_INTERFACE_VERSION\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"}],\"name\":\"accessListAdd\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"accessListEnabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"}],\"name\":\"accessListRemove\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"}],\"name\":\"blockListAdd\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"accounts\",\"type\":\"address[]\"}],\"name\":\"blockListRemove\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"burnFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"contractUri\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_uri\",\"type\":\"string\"}],\"name\":\"contractUriUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAccessList\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getBlockList\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"getRoleMember\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMemberCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleMembers\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_symbol\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"_uri\",\"type\":\"string\"},{\"internalType\":\"address\",\"name\":\"admin\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isAccessListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isBlockListed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes[]\",\"name\":\"data\",\"type\":\"bytes[]\"}],\"name\":\"multicall\",\"outputs\":[{\"internalType\":\"bytes[]\",\"name\":\"results\",\"type\":\"bytes[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxiableUUID\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"salvageERC20\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"salvageNative\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"enabled\",\"type\":\"bool\"}],\"name\":\"toggleAccesslist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"Cobo Dev Team https://www.cobo.com/ This contract Role Based Access Control employs following roles:  - MINTER_ROLE  - BURNER_ROLE  - MANAGER_ROLE  - SALVAGER_ROLE  - PAUSER_ROLE  - UPGRADER_ROLE  - DEFAULT_ADMIN_ROLE\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"AddressEmptyCode(address)\":[{\"details\":\"There's no code at `target` (it is not a contract).\"}],\"BlockedAddress(address)\":[{\"details\":\"Indicates a failure that an address is blocked.\"}],\"DefaultAdminError()\":[{\"details\":\"Indicates a failure because \\\"DEFAULT_ADMIN_ROLE\\\" was tried to be revoked.\"}],\"ERC1967InvalidImplementation(address)\":[{\"details\":\"The `implementation` of the proxy is invalid.\"}],\"ERC1967NonPayable()\":[{\"details\":\"An upgrade function sees `msg.value > 0` that may be lost.\"}],\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"FailedCall()\":[{\"details\":\"A call to an address target failed. The target may have reverted.\"}],\"InvalidAddress()\":[{\"details\":\"Indicates a failure that an address is not valid.\"}],\"InvalidInitialization()\":[{\"details\":\"The contract is already initialized.\"}],\"NotAccessListAddress(address)\":[{\"details\":\"Indicates a failure that an address is not in the access list.\"}],\"NotInitializing()\":[{\"details\":\"The contract is not initializing.\"}],\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}],\"SalvageNativeFailed()\":[{\"details\":\"Indicates a failure while salvaging native token.\"}],\"UUPSUnauthorizedCallContext()\":[{\"details\":\"The call is from an unauthorized context.\"}],\"UUPSUnsupportedProxiableUUID(bytes32)\":[{\"details\":\"The storage `slot` is unsupported as a UUID.\"}],\"ZeroAmount()\":[{\"details\":\"Indicates a failure that a value is zero.\"}]},\"events\":{\"AccessListAddressAdded(address)\":{\"details\":\"Notifies that the ability of logged address to participant is changed as per the implementation contract.\",\"params\":{\"account\":\"The (indexed) address which was added to the Access list.\"}},\"AccessListAddressRemoved(address)\":{\"details\":\"Notifies that the ability of logged address to participant is changed as per the implementation contract.\",\"params\":{\"account\":\"The (indexed) address which was removed from the Access list.\"}},\"AccesslistToggled(bool)\":{\"details\":\"Notifies that the access list is enabled or disabled.\",\"params\":{\"enabled\":\"The (indexed) boolean value indicating whether the access list is enabled.\"}},\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"BlockListAddressAdded(address)\":{\"details\":\"Notifies that the ability of logged address to participant is changed as per the implementation contract.\",\"params\":{\"account\":\"The (indexed) address which was added to the Block list.\"}},\"BlockListAddressRemoved(address)\":{\"details\":\"Notifies that the ability of logged address to participant is changed as per the implementation contract.\",\"params\":{\"account\":\"The (indexed) address which was removed from the Block list.\"}},\"ContractUriUpdated(address,string,string)\":{\"params\":{\"caller\":\"The (indexed) address of the entity that triggered the update.\",\"newUri\":\"The new URI associated with the contract.\",\"oldUri\":\"The URI previously associated with the contract.\"}},\"Initialized(uint64)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"NativeSalvaged(address,uint256)\":{\"params\":{\"amount\":\"The (indexed) amount of native token salvaged.\",\"caller\":\"The (indexed) address of the entity that triggered the salvage.\"}},\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"},\"TokenSalvaged(address,address,uint256)\":{\"params\":{\"amount\":\"The (indexed) amount of tokens salvaged.\",\"caller\":\"The (indexed) address of the entity that triggered the salvage.\",\"token\":\"The (indexed) address of the ERC20 token which was salvaged.\"}},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"},\"Upgraded(address)\":{\"details\":\"Emitted when the implementation is upgraded.\"}},\"kind\":\"dev\",\"methods\":{\"accessListAdd(address[])\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. - All the addresses in the`accounts` array must be a non-zero address. This function emits a {AccessListAddressAdded} event only when it successfully adds an address to the `_accessList` mapping, given that the address was previously not present on AccessList.\",\"params\":{\"accounts\":\"The list addresses to be added to the AccessList.\"}},\"accessListRemove(address[])\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. This function emits a {AccessListAddressRemoved} event only when it successfully removes an address from the `_accessList` mapping, given that the address was previously present on AccessList.\",\"params\":{\"accounts\":\"The list addresses to be removed from the AccessList.\"}},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"blockListAdd(address[])\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. - All the addresses in the`accounts` array must be a non-zero address. This function emits a {BlockListAddressAdded} event only when it successfully adds an address to the `_blockList` mapping, given that the address was previously not present on BlockList.\",\"params\":{\"accounts\":\"The list addresses to be added to the BlockList.\"}},\"blockListRemove(address[])\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. This function emits a {BlockListAddressRemoved} event only when it successfully removes an address from the `_blockList` mapping, given that the address was previously present on BlockList.\",\"params\":{\"accounts\":\"The list addresses to be removed from the BlockList.\"}},\"burn(uint256)\":{\"details\":\"Calling Conditions: - Can only be invoked by the address that has the role \\\"BURNER_ROLE\\\". - `amount` is less than or equal to the caller's balance. (checked internally by {ERC20Upgradeable}.{_burn}) - `amount` is greater than 0. This function emits a {Transfer} event as part of {ERC20Upgradeable._burn}.\",\"params\":{\"amount\":\"The number of tokens to be burned.\"}},\"burnFrom(address,uint256)\":{\"details\":\"Calling Conditions: - Can only be invoked by the address that has the role \\\"MANAGER_ROLE\\\". - `amount` is less than or equal to the specified address's balance. (checked internally by {ERC20Upgradeable}.{_burn}) - `amount` is greater than 0. This function emits a {Transfer} event as part of {ERC20Upgradeable._burn}.\",\"params\":{\"account\":\"The address that will have their tokens burned.\",\"amount\":\"The number of tokens to be burned.\"}},\"constructor\":{\"custom:oz-upgrades-unsafe-allow\":\"constructor\"},\"contractUriUpdate(string)\":{\"details\":\"This function emits a {ContractUriUpdated} event. The caller must hold the \\\"MANAGER_ROLE\\\" role.\",\"params\":{\"_uri\":\"A URI link pointing to the current URI associated with the contract.\"}},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"getAccessList()\":{\"details\":\"This function returns the list of addresses that are in the `_accessList`. Note: This is designed to be a helper function that is called from off-chain. If the `_accessList` is large, this function will consume a lot of gas or revert.\",\"returns\":{\"_0\":\"The list of addresses that are in the access list.\"}},\"getBlockList()\":{\"details\":\"This function returns the list of addresses that are in the `_blockList`. Note: This is designed to be a helper function that is called from off-chain. If the `_blockList` is large, this function will consume a lot of gas or revert.\",\"returns\":{\"_0\":\"The list of addresses that are in the BlockList.\"}},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getRoleMember(bytes32,uint256)\":{\"details\":\"Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information.\"},\"getRoleMemberCount(bytes32)\":{\"details\":\"Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role.\"},\"getRoleMembers(bytes32)\":{\"details\":\"Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"initialize(string,string,string,address)\":{\"details\":\"Calling Conditions: - Can only be invoked once (controlled via the {initializer} modifier).\",\"params\":{\"_name\":\"The name of the token.\",\"_symbol\":\"The symbol of the token.\"}},\"isAccessListed(address)\":{\"details\":\"This function returns `true` if the address is present in the AccessList, otherwise it returns `false`.\",\"params\":{\"account\":\"The address to be checked.\"},\"returns\":{\"_0\":\"`true` if the address is present in the AccessList, otherwise it returns `false`.\"}},\"isBlockListed(address)\":{\"details\":\"This function returns `true` if the address is present in the BlockList, otherwise it returns `false`.\",\"params\":{\"account\":\"The address to be checked.\"},\"returns\":{\"_0\":\"`true` if the address is present in the BlockList, otherwise it returns `false`.\"}},\"mint(address,uint256)\":{\"details\":\"Calling Conditions: - Can only be invoked by the address that has the role \\\"MINTER_ROLE\\\". - {CoboERC20} is not paused. - `to` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_mint}) - `to` is allowed to receive tokens. This function emits a {Transfer} event as part of {ERC20Upgradeable._mint}.\",\"params\":{\"amount\":\"The number of tokens to be issued.\",\"to\":\"The address that will receive the issued tokens.\"}},\"multicall(bytes[])\":{\"custom:oz-upgrades-unsafe-allow-reachable\":\"delegatecall\",\"details\":\"Receives and executes a batch of function calls on this contract.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"pause()\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. This function emits a {Paused} event as part of {PausableUpgradeable._pause}.\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"proxiableUUID()\":{\"details\":\"Implementation of the ERC-1822 {proxiableUUID} function. This returns the storage slot used by the implementation. It is used to validate the implementation's compatibility when performing an upgrade. IMPORTANT: A proxy pointing at a proxiable contract should not be considered proxiable itself, because this risks bricking a proxy that upgrades to it, by delegating to itself until out of gas. Thus it is critical that this function revert if invoked through a proxy. This is guaranteed by the `notDelegated` modifier.\"},\"renounceRole(bytes32,address)\":{\"details\":\"Only the account itself can renounce its own roles, and not any other account.  Calling Conditions: - The `account` must be the caller of the transaction. - The `account` cannot renounce the \\\"DEFAULT_ADMIN_ROLE\\\".\"},\"revokeRole(bytes32,address)\":{\"details\":\"Calling Conditions: - The caller must be the role admin of the `role`. - The caller must not be the `account` itself with the \\\"DEFAULT_ADMIN_ROLE\\\". - The `account` must be a non-zero address. This function emits a {RoleRevoked} event as part of {AccessControlUpgradeable._revokeRole}.\",\"params\":{\"account\":\"The address from which role is revoked\",\"role\":\"The role that will be revoked.\"}},\"salvageERC20(address,uint256)\":{\"details\":\"Calling Conditions: - The `amount` must be greater than 0. This function emits a {TokenSalvaged} event, indicating that funds were salvaged.\",\"params\":{\"amount\":\"The amount to be salvaged.\",\"token\":\"The ERC20 asset which is to be salvaged.\"}},\"salvageNative(uint256)\":{\"details\":\"Calling Conditions: - The `amount` must be greater than 0. This function emits a {NativeSalvaged} event, indicating that funds were salvaged.\",\"params\":{\"amount\":\"The amount to be salvaged.\"}},\"supportsInterface(bytes4)\":{\"details\":\"This function returns `true` if the interface is supported, otherwise it returns `false`.\",\"returns\":{\"_0\":\"`true` if the interface is supported, otherwise it returns `false`.\"}},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"toggleAccesslist(bool)\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. This function emits a {AccesslistToggled} event only when it successfully toggles the access list.\",\"params\":{\"enabled\":\"The boolean value indicating whether the access list is enabled.\"}},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Calling Conditions: - {CoboERC20} is not paused. - The `sender` is allowed to send tokens. (checked internally by {_requireAccess}) - The `to` is allowed to receive tokens. (checked internally by {_requireAccess}) - `to` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_transfer}) - `amount` is not greater than sender's balance. (checked internally by {ERC20Upgradeable}.{_transfer}) This function emits a {Transfer} event as part of {ERC20Upgradeable._transfer}.\",\"params\":{\"amount\":\"The number of tokens that will be sent to the `to` address.\",\"to\":\"The address that will receive the tokens.\"},\"returns\":{\"_0\":\"True if the function was successful.\"}},\"transferFrom(address,address,uint256)\":{\"details\":\"Calling Conditions: - {CoboERC20} is not paused. - The `from` is allowed to send tokens. (checked internally by {_requireAccess}) - The `to` is allowed to receive tokens. (checked internally by {_requireAccess}) - `from` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_transfer}) - `to` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_transfer}) - `amount` is not greater than `from`'s balance or caller's allowance of `from`'s funds. (checked internally   by {ERC20Upgradeable}.{transferFrom}) - `amount` is greater than 0. (checked internally by {_spendAllowance})\",\"params\":{\"amount\":\"The number of tokens that will be sent to the `to` address.\",\"from\":\"The address that tokens will be transferred on behalf of.\",\"to\":\"The address that will receive the tokens.\"},\"returns\":{\"_0\":\"True if the function was successful.\"}},\"unpause()\":{\"details\":\"Calling Conditions: - The caller must hold the \\\"MANAGER_ROLE\\\" role. This function emits an {Unpaused} event as part of {PausableUpgradeable._unpause}.\"},\"upgradeToAndCall(address,bytes)\":{\"custom:oz-upgrades-unsafe-allow-reachable\":\"delegatecall\",\"details\":\"Upgrade the implementation of the proxy to `newImplementation`, and subsequently execute the function call encoded in `data`. Calls {_authorizeUpgrade}. Emits an {Upgraded} event.\"},\"version()\":{\"details\":\"This function get the latest deployment version from the {Initializable}.{_getInitializedVersion}. With every new deployment, the version number will be incremented.\",\"returns\":{\"_0\":\"The version of the contract.\"}}},\"stateVariables\":{\"BURNER_ROLE\":{\"details\":\"This constant holds the hash of the string \\\"BURNER_ROLE\\\".\"},\"MANAGER_ROLE\":{\"details\":\"This constant holds the hash of the string \\\"MANAGER_ROLE\\\".\"},\"MINTER_ROLE\":{\"details\":\"This constant holds the hash of the string \\\"MINTER_ROLE\\\".\"},\"PAUSER_ROLE\":{\"details\":\"This constant holds the hash of the string \\\"PAUSER_ROLE\\\".\"},\"SALVAGER_ROLE\":{\"details\":\"This constant holds the hash of the string \\\"SALVAGER_ROLE\\\".\"},\"UPGRADER_ROLE\":{\"details\":\"This constant holds the hash of the string \\\"UPGRADER_ROLE\\\".\"}},\"title\":\"CoboERC20\",\"version\":1},\"userdoc\":{\"events\":{\"AccessListAddressAdded(address)\":{\"notice\":\"This event is logged when an address is added to the Access list.\"},\"AccessListAddressRemoved(address)\":{\"notice\":\"This event is logged when an address is removed from the Access list.\"},\"AccesslistToggled(bool)\":{\"notice\":\"This event is logged when the access list is toggled.\"},\"BlockListAddressAdded(address)\":{\"notice\":\"This event is logged when an address is added to the Block list.\"},\"BlockListAddressRemoved(address)\":{\"notice\":\"This event is logged when an address is removed from the Block list.\"},\"ContractUriUpdated(address,string,string)\":{\"notice\":\"This event is logged when the contract URI is updated.\"},\"NativeSalvaged(address,uint256)\":{\"notice\":\"This event is logged when Native token is salvaged.\"},\"TokenSalvaged(address,address,uint256)\":{\"notice\":\"This event is logged when ERC20 tokens are salvaged.\"}},\"kind\":\"user\",\"methods\":{\"BURNER_ROLE()\":{\"notice\":\"The Access Control identifier for the Burner Role. An account with \\\"BURNER_ROLE\\\" can burn tokens from their own address.\"},\"MANAGER_ROLE()\":{\"notice\":\"The Access Control identifier for the Manager Role. An account with \\\"MANAGER_ROLE\\\" can update the contract URI, unpause the contract, toggle the access list status, update the access list, and update the block list.\"},\"MINTER_ROLE()\":{\"notice\":\"The Access Control identifier for the Minter Role. An account with \\\"MINTER_ROLE\\\" can mint tokens to the specified address.\"},\"PAUSER_ROLE()\":{\"notice\":\"The Access Control identifier for the Pauser Role. An account with \\\"PAUSER_ROLE\\\" can pause the contract.\"},\"SALVAGER_ROLE()\":{\"notice\":\"The Access Control identifier for the Salvager Role. An account with \\\"SALVAGER_ROLE\\\" can salvage native and ERC20 tokens from the contract.\"},\"UPGRADER_ROLE()\":{\"notice\":\"The Access Control identifier for the Upgrader Role. An account with \\\"UPGRADER_ROLE\\\" can upgrade the implementation contract address.\"},\"accessListAdd(address[])\":{\"notice\":\"This function adds a list of given address to the AccessList. This will allow the specified addresses to interact with the CoboERC20 contract. The function can be called by the address which has the \\\"MANAGER_ROLE\\\". The access list is disabled by default.\"},\"accessListEnabled()\":{\"notice\":\"State\"},\"accessListRemove(address[])\":{\"notice\":\"This function removes a list of given address from the AccessList. The function can be called by the address which has the \\\"MANAGER_ROLE\\\".\"},\"blockListAdd(address[])\":{\"notice\":\"This function adds a list of given address to the BlockList. This will block the specified addresses from interacting with the CoboERC20 contract. The function can be called by the address which has the \\\"MANAGER_ROLE\\\".\"},\"blockListRemove(address[])\":{\"notice\":\"This function removes a list of given address from the BlockList. The function can be called by the address which has the \\\"MANAGER_ROLE\\\".\"},\"burn(uint256)\":{\"notice\":\"This is a function used to burn tokens. The caller will burn tokens from their own address.\"},\"burnFrom(address,uint256)\":{\"notice\":\"This is a function used to burn tokens from the specified address. The caller will burn tokens from the specified address.\"},\"contractUri()\":{\"notice\":\"This field is a URI (Uniform Resource Identifier) that points to a JSON file with metadata about the contract.\"},\"contractUriUpdate(string)\":{\"notice\":\"This is a function used to update `contractUri` field.\"},\"getAccessList()\":{\"notice\":\"This function returns the list of addresses that are in the access list.\"},\"getBlockList()\":{\"notice\":\"This function returns the list of addresses that are in the BlockList.\"},\"initialize(string,string,string,address)\":{\"notice\":\"This function configures the CoboERC20 contract with the initial state and granting privileged roles.\"},\"isAccessListed(address)\":{\"notice\":\"This function checks if an address is present in the AccessList. By doing so, it confirms that whether the  address is allowed to participate in the system.\"},\"isBlockListed(address)\":{\"notice\":\"This function checks if an address is present in the BlockList. By doing so, it confirms that whether the  address is blocked from participating in the system.\"},\"mint(address,uint256)\":{\"notice\":\"This is a function used to issue new tokens. The caller will issue tokens to the `to` address.\"},\"pause()\":{\"notice\":\"This is a function used to pause the contract.\"},\"renounceRole(bytes32,address)\":{\"notice\":\"This function renounces an Access Control role from an account, except for the \\\"DEFAULT_ADMIN_ROLE\\\".\"},\"revokeRole(bytes32,address)\":{\"notice\":\"This function revokes an Access Control role from an account, except for the _msgSender()'s \\\"DEFAULT_ADMIN_ROLE\\\".\"},\"salvageERC20(address,uint256)\":{\"notice\":\"A function used to salvage ERC20 tokens sent to the contract using this abstract contract.\"},\"salvageNative(uint256)\":{\"notice\":\"A function used to salvage native token sent to the contract using this abstract contract.\"},\"supportsInterface(bytes4)\":{\"notice\":\"This is a function used to check if an interface is supported by this contract.\"},\"toggleAccesslist(bool)\":{\"notice\":\"This function toggles the access list. The function can be called by the address which has the \\\"MANAGER_ROLE\\\". The access list is disabled by default.\"},\"transfer(address,uint256)\":{\"notice\":\"This is a function used to transfer tokens from the sender to the `to` address.\"},\"transferFrom(address,address,uint256)\":{\"notice\":\"This is a function used to transfer tokens on behalf of the `from` address to the `to` address. This function emits an {Approval} event as part of {ERC20Upgradeable._approve}. This function emits a {Transfer} event as part of {ERC20Upgradeable._transfer}.\"},\"unpause()\":{\"notice\":\"This is a function used to unpause the contract.\"},\"version()\":{\"notice\":\"This is a function used to get the version of the contract.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/CoboERC20/CoboERC20.sol\":\"CoboERC20\"},\"evmVersion\":\"london\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":20000},\"remappings\":[\":@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/\",\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":ds-test/=lib/forge-std/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"],\"viaIR\":true},\"sources\":{\"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol\":{\"keccak256\":\"0xca0f8798297e39106cad374524780916b7ea20033641df3a9102aae253099aeb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://77d5c809873db24e02fc288ad506087853911f5daf0fb27e499333b4e75e3af1\",\"dweb:/ipfs/QmWNqoEj7NH3neSNNfoHwXfynrGqJu5JsvMfHe8fZB7isR\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol\":{\"keccak256\":\"0xce21b42f07ce8d05b7fa3ac8581d8c26f9458a0d80e9d1c75d73b96612e0ed9e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://133fedaa75f3f5d307f6872c47f6c12bdf9f5c8df01b1357fc283d2dccbfaa33\",\"dweb:/ipfs/QmbPS26D88gd5ahpedRC2YByajf4gWJ7EQvHoQwZeneadF\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol\":{\"keccak256\":\"0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08\",\"dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/UUPSUpgradeable.sol\":{\"keccak256\":\"0x574a7451e42724f7de29e2855c392a8a5020acd695169466a18459467d719d63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5bc189f63b639ee173dd7b6fecc39baf7113bf161776aea22b34c57fdd1872ec\",\"dweb:/ipfs/QmZAf2VtjDLRULqjJkde6LNsxAg12tUqpPqgUQQZbAjgtZ\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol\":{\"keccak256\":\"0x776f40d448eba02791172036fcc6dfbe42fd4dec32b18ba49879d6ed474271ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://286b8b7de752a268ce097ce87a0979d1f9c37d052cff0a4b9bd59fd0c34232ec\",\"dweb:/ipfs/QmUti8SGn5TFBNSPAwB2EBmUW6CYCU6sUKgK9iFdFxhjMJ\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol\":{\"keccak256\":\"0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9\",\"dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/MulticallUpgradeable.sol\":{\"keccak256\":\"0xe5775eb1fb17165cd191e8f8b2232dbea8765e7e610eaa3d6e52feead793ec5a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://352614aea75c3d913cbcabb528be3d6c3335c3c77da41d59486a3193069dd095\",\"dweb:/ipfs/QmR3Nabxfme6tHrAMJCyK4MWZtpund2c4R7aFKmea3sGZM\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/PausableUpgradeable.sol\":{\"keccak256\":\"0xa6bf6b7efe0e6625a9dcd30c5ddf52c4c24fe8372f37c7de9dbf5034746768d5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8c353ee3705bbf6fadb84c0fb10ef1b736e8ca3ca1867814349d1487ed207beb\",\"dweb:/ipfs/QmcugaPssrzGGE8q4YZKm2ZhnD3kCijjcgdWWg76nWt3FY\"]},\"lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol\":{\"keccak256\":\"0x2f0ea26703f46fc430ad9202a63f07521d234b9a94a1a7b019f4973bed7a35a0\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ccf228512ceb777b145d339b50b8b6a72393140cdd61bcb7bf842a89230e4fe7\",\"dweb:/ipfs/QmeWaUw193GDphNkPhJnpHQpfJBrv4v2QRbfGTdfYNTiEE\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol\":{\"keccak256\":\"0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3\",\"dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://da62d6be1f5c6edf577f0cb45666a8aa9c2086a4bac87d95d65f02e2f4c36a4b\",\"dweb:/ipfs/QmNkpvBpoCMvX8JwAFNSc5XxJ2q5BXJpL5L1txb4QkqVFF\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://11fea9f8bc98949ac6709f0c1699db7430d2948137aa94d5a9e95a91f61a710a\",\"dweb:/ipfs/QmQdfRXxQjwP6yn3DVo1GHPpriKNcFghSPi94Z1oKEFUNS\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol\":{\"keccak256\":\"0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d\",\"dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a2608291cb038b388d80b79a06b6118a42f7894ff67b7da10ec0dbbf5b2973ba\",\"dweb:/ipfs/QmWohqcBLbcxmA4eGPhZDXe5RYMMEEpFq22nfkaUMvTfw1\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol\":{\"keccak256\":\"0xc42facb5094f2f35f066a7155bda23545e39a3156faef3ddc00185544443ba7d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3b36282ab029b46bd082619a308a2ea11c309967b9425b7b7a6eb0b0c1c3196\",\"dweb:/ipfs/QmP2YVfDB2FoREax3vJu7QhDnyYRMw52WPrCD4vdT2kuDA\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b\",\"dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz\"]},\"lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol\":{\"keccak256\":\"0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70\",\"dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2\"]},\"lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol\":{\"keccak256\":\"0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa\",\"dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db\",\"dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf\",\"dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508\",\"dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB\"]},\"lib/openzeppelin-contracts/contracts/utils/Address.sol\":{\"keccak256\":\"0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e\",\"dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR\"]},\"lib/openzeppelin-contracts/contracts/utils/Arrays.sol\":{\"keccak256\":\"0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd\",\"dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN\"]},\"lib/openzeppelin-contracts/contracts/utils/Comparators.sol\":{\"keccak256\":\"0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd\",\"dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC\"]},\"lib/openzeppelin-contracts/contracts/utils/Errors.sol\":{\"keccak256\":\"0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf\",\"dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol\":{\"keccak256\":\"0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2\",\"dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol\":{\"keccak256\":\"0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231\",\"dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y\"]},\"src/CoboERC20/CoboERC20.sol\":{\"keccak256\":\"0x328d6a1550967347dbe6e7ca8c9bd7b80e73c5257c563842fb083d06495436ed\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://ce35c5bde3dbb5e6a07c944ce8c54f58afaeefac93e5af54c1327d92374281a5\",\"dweb:/ipfs/QmRhnQsjWGKWys8GAStAE4Dqg6VYh2stjqbjPPRs75DWTY\"]},\"src/CoboERC20/library/Errors/LibErrors.sol\":{\"keccak256\":\"0x54a1b1b5b2e1d8ef12871c6d44f8087235022c0e41cfd0bf081f5802a560cbd6\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://c4c0c9a4d3217ba26cf466a8245b54636f456de5f4d8c0c356998bd8ba2fc85b\",\"dweb:/ipfs/QmTvGC9oTkgGB9CMVvkpSHYZZv9sGmPmzRVznkueS1WCcu\"]},\"src/CoboERC20/library/Utils/AccessListUpgradeable.sol\":{\"keccak256\":\"0x2765e89ce9d2f7bbfea3b662a3e98e754c5360e04ff020158fd9b1faea926621\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://b9be8dc8b48947360efa4d71c4a33e7e3cf57ed37db77547bf3285808627a96c\",\"dweb:/ipfs/Qmea8FS9HR1xj5mfJ4dCMuWq7wJgsGL19PnXcMjWwm84VA\"]},\"src/CoboERC20/library/Utils/ContractUriUpgradeable.sol\":{\"keccak256\":\"0xe57a2a3a611c23b1e95b81544bbdded20690c58f0acf91a084dc57c1de1ea850\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://bb4803cf2fd067f99dca3ed028e45e26fc27e43c3bd118360694e7083564f252\",\"dweb:/ipfs/QmcVC2PhWji6nWNj7fAz61fgBuc8JZmCfj7NNeUwZ9ru5u\"]},\"src/CoboERC20/library/Utils/PauseUpgradeable.sol\":{\"keccak256\":\"0x6fa557a4d62792cf029371b95eb5842e56bd7d4ac773684a81a7069942752e20\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://130678ddd6592952a59ef7c0ff8fc7cf3aef47bef91595340f6e8d091016656b\",\"dweb:/ipfs/QmWhoGHo4yMgtU1dK3mNEDHqZ474MWXEuT3sUTJxdBP1Rf\"]},\"src/CoboERC20/library/Utils/RoleAccessUpgradeable.sol\":{\"keccak256\":\"0xa04888487c9afc8c4a88ad2ef8be02e614b08c71d3c5876cba4e9231788a5f96\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://dd21af034114e3d2d4d64a268c2091d33d15f4bd8927546db9daa715bdbf37b7\",\"dweb:/ipfs/QmbCVy1463saVfiydAmDszFKEVXJp9BY6QvD2FSScEKKm3\"]},\"src/CoboERC20/library/Utils/SalvageUpgradeable.sol\":{\"keccak256\":\"0x4af66e1af93a0df1c4a90fec0e6b1cc31209ac5d530030537784b930bb647192\",\"license\":\"LGPL-3.0-only\",\"urls\":[\"bzz-raw://3d7f046c53c3acab26e0f2820b17da61ec008e53c8055d947d20bd6a24ac0fd5\",\"dweb:/ipfs/QmevZ8iweUsTRHrGpd4vnEBAooiiVssbTgwUKuzazAaiSX\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.23+commit.f704f362"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "type": "error", "name": "AddressEmptyCode"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "BlockedAddress"}, {"inputs": [], "type": "error", "name": "DefaultAdminError"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "type": "error", "name": "ERC1967InvalidImplementation"}, {"inputs": [], "type": "error", "name": "ERC1967Non<PERSON>ayable"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [], "type": "error", "name": "EnforcedPause"}, {"inputs": [], "type": "error", "name": "ExpectedPause"}, {"inputs": [], "type": "error", "name": "FailedCall"}, {"inputs": [], "type": "error", "name": "InvalidAddress"}, {"inputs": [], "type": "error", "name": "InvalidInitialization"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "NotAccessListAddress"}, {"inputs": [], "type": "error", "name": "NotInitializing"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [], "type": "error", "name": "SalvageNativeFailed"}, {"inputs": [], "type": "error", "name": "UUPSUnauthorizedCallContext"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "type": "error", "name": "UUPSUnsupportedProxiableUUID"}, {"inputs": [], "type": "error", "name": "ZeroAmount"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "AccessListAddressAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "AccessListAddressRemoved", "anonymous": false}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool", "indexed": true}], "type": "event", "name": "AccesslistToggled", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "BlockListAddressAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": true}], "type": "event", "name": "BlockListAddressRemoved", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "string", "name": "old<PERSON><PERSON>", "type": "string", "indexed": false}, {"internalType": "string", "name": "newUri", "type": "string", "indexed": false}], "type": "event", "name": "ContractUriUpdated", "anonymous": false}, {"inputs": [{"internalType": "uint64", "name": "version", "type": "uint64", "indexed": false}], "type": "event", "name": "Initialized", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": true}], "type": "event", "name": "NativeSalvaged", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Paused", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "caller", "type": "address", "indexed": true}, {"internalType": "address", "name": "token", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "amount", "type": "uint256", "indexed": true}], "type": "event", "name": "TokenSalvaged", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Unpaused", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address", "indexed": true}], "type": "event", "name": "Upgraded", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "BURNER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SALVAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "UPGRADER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "accessListAdd"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "accessListEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "accessListRemove"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "blockListAdd"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}], "stateMutability": "nonpayable", "type": "function", "name": "blockListRemove"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burnFrom"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "contractUri", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "string", "name": "_uri", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "contractUriUpdate"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getAccessList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "getBlockList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "string", "name": "_uri", "type": "string"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "initialize"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isAccessListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "isBlockListed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [{"internalType": "bytes[]", "name": "data", "type": "bytes[]"}], "stateMutability": "nonpayable", "type": "function", "name": "multicall", "outputs": [{"internalType": "bytes[]", "name": "results", "type": "bytes[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pause"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "salvageERC20"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "salvageNative"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}], "stateMutability": "nonpayable", "type": "function", "name": "toggleAccesslist"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unpause"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "payable", "type": "function", "name": "upgradeToAndCall"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "version", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}], "devdoc": {"kind": "dev", "methods": {"accessListAdd(address[])": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. - All the addresses in the`accounts` array must be a non-zero address. This function emits a {AccessListAddressAdded} event only when it successfully adds an address to the `_accessList` mapping, given that the address was previously not present on AccessList.", "params": {"accounts": "The list addresses to be added to the AccessList."}}, "accessListRemove(address[])": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. This function emits a {AccessListAddressRemoved} event only when it successfully removes an address from the `_accessList` mapping, given that the address was previously present on AccessList.", "params": {"accounts": "The list addresses to be removed from the AccessList."}}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "blockListAdd(address[])": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. - All the addresses in the`accounts` array must be a non-zero address. This function emits a {BlockListAddressAdded} event only when it successfully adds an address to the `_blockList` mapping, given that the address was previously not present on BlockList.", "params": {"accounts": "The list addresses to be added to the BlockList."}}, "blockListRemove(address[])": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. This function emits a {BlockListAddressRemoved} event only when it successfully removes an address from the `_blockList` mapping, given that the address was previously present on BlockList.", "params": {"accounts": "The list addresses to be removed from the BlockList."}}, "burn(uint256)": {"details": "Calling Conditions: - Can only be invoked by the address that has the role \"BURNER_ROLE\". - `amount` is less than or equal to the caller's balance. (checked internally by {ERC20Upgradeable}.{_burn}) - `amount` is greater than 0. This function emits a {Transfer} event as part of {ERC20Upgradeable._burn}.", "params": {"amount": "The number of tokens to be burned."}}, "burnFrom(address,uint256)": {"details": "Calling Conditions: - Can only be invoked by the address that has the role \"MANAGER_ROLE\". - `amount` is less than or equal to the specified address's balance. (checked internally by {ERC20Upgradeable}.{_burn}) - `amount` is greater than 0. This function emits a {Transfer} event as part of {ERC20Upgradeable._burn}.", "params": {"account": "The address that will have their tokens burned.", "amount": "The number of tokens to be burned."}}, "constructor": {"custom:oz-upgrades-unsafe-allow": "constructor"}, "contractUriUpdate(string)": {"details": "This function emits a {ContractUriUpdated} event. The caller must hold the \"MANAGER_ROLE\" role.", "params": {"_uri": "A URI link pointing to the current URI associated with the contract."}}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "getAccessList()": {"details": "This function returns the list of addresses that are in the `_accessList`. Note: This is designed to be a helper function that is called from off-chain. If the `_accessList` is large, this function will consume a lot of gas or revert.", "returns": {"_0": "The list of addresses that are in the access list."}}, "getBlockList()": {"details": "This function returns the list of addresses that are in the `_blockList`. Note: This is designed to be a helper function that is called from off-chain. If the `_blockList` is large, this function will consume a lot of gas or revert.", "returns": {"_0": "The list of addresses that are in the BlockList."}}, "getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "getRoleMember(bytes32,uint256)": {"details": "Returns one of the accounts that have `role`. `index` must be a value between 0 and {getRoleMemberCount}, non-inclusive. Role bearers are not sorted in any particular way, and their ordering may change at any point. WARNING: When using {getRoleMember} and {getRoleMemberCount}, make sure you perform all queries on the same block. See the following https://forum.openzeppelin.com/t/iterating-over-elements-on-enumerableset-in-openzeppelin-contracts/2296[forum post] for more information."}, "getRoleMemberCount(bytes32)": {"details": "Returns the number of accounts that have `role`. Can be used together with {getRoleMember} to enumerate all bearers of a role."}, "getRoleMembers(bytes32)": {"details": "Return all accounts that have `role` WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that this function has an unbounded cost, and using it as part of a state-changing function may render the function uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block."}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "initialize(string,string,string,address)": {"details": "Calling Conditions: - Can only be invoked once (controlled via the {initializer} modifier).", "params": {"_name": "The name of the token.", "_symbol": "The symbol of the token."}}, "isAccessListed(address)": {"details": "This function returns `true` if the address is present in the AccessList, otherwise it returns `false`.", "params": {"account": "The address to be checked."}, "returns": {"_0": "`true` if the address is present in the AccessList, otherwise it returns `false`."}}, "isBlockListed(address)": {"details": "This function returns `true` if the address is present in the BlockList, otherwise it returns `false`.", "params": {"account": "The address to be checked."}, "returns": {"_0": "`true` if the address is present in the BlockList, otherwise it returns `false`."}}, "mint(address,uint256)": {"details": "Calling Conditions: - Can only be invoked by the address that has the role \"MINTER_ROLE\". - {CoboERC20} is not paused. - `to` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_mint}) - `to` is allowed to receive tokens. This function emits a {Transfer} event as part of {ERC20Upgradeable._mint}.", "params": {"amount": "The number of tokens to be issued.", "to": "The address that will receive the issued tokens."}}, "multicall(bytes[])": {"custom:oz-upgrades-unsafe-allow-reachable": "delegatecall", "details": "Receives and executes a batch of function calls on this contract."}, "name()": {"details": "Returns the name of the token."}, "pause()": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. This function emits a {Paused} event as part of {PausableUpgradeable._pause}."}, "paused()": {"details": "Returns true if the contract is paused, and false otherwise."}, "proxiableUUID()": {"details": "Implementation of the ERC-1822 {proxiableUUID} function. This returns the storage slot used by the implementation. It is used to validate the implementation's compatibility when performing an upgrade. IMPORTANT: A proxy pointing at a proxiable contract should not be considered proxiable itself, because this risks bricking a proxy that upgrades to it, by delegating to itself until out of gas. Thus it is critical that this function revert if invoked through a proxy. This is guaranteed by the `notDelegated` modifier."}, "renounceRole(bytes32,address)": {"details": "Only the account itself can renounce its own roles, and not any other account.  Calling Conditions: - The `account` must be the caller of the transaction. - The `account` cannot renounce the \"DEFAULT_ADMIN_ROLE\"."}, "revokeRole(bytes32,address)": {"details": "Calling Conditions: - The caller must be the role admin of the `role`. - The caller must not be the `account` itself with the \"DEFAULT_ADMIN_ROLE\". - The `account` must be a non-zero address. This function emits a {RoleRevoked} event as part of {AccessControlUpgradeable._revokeRole}.", "params": {"account": "The address from which role is revoked", "role": "The role that will be revoked."}}, "salvageERC20(address,uint256)": {"details": "Calling Conditions: - The `amount` must be greater than 0. This function emits a {TokenSalvaged} event, indicating that funds were salvaged.", "params": {"amount": "The amount to be salvaged.", "token": "The ERC20 asset which is to be salvaged."}}, "salvageNative(uint256)": {"details": "Calling Conditions: - The `amount` must be greater than 0. This function emits a {NativeSalvaged} event, indicating that funds were salvaged.", "params": {"amount": "The amount to be salvaged."}}, "supportsInterface(bytes4)": {"details": "This function returns `true` if the interface is supported, otherwise it returns `false`.", "returns": {"_0": "`true` if the interface is supported, otherwise it returns `false`."}}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "toggleAccesslist(bool)": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. This function emits a {AccesslistToggled} event only when it successfully toggles the access list.", "params": {"enabled": "The boolean value indicating whether the access list is enabled."}}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "Calling Conditions: - {CoboERC20} is not paused. - The `sender` is allowed to send tokens. (checked internally by {_requireAccess}) - The `to` is allowed to receive tokens. (checked internally by {_requireAccess}) - `to` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_transfer}) - `amount` is not greater than sender's balance. (checked internally by {ERC20Upgradeable}.{_transfer}) This function emits a {Transfer} event as part of {ERC20Upgradeable._transfer}.", "params": {"amount": "The number of tokens that will be sent to the `to` address.", "to": "The address that will receive the tokens."}, "returns": {"_0": "True if the function was successful."}}, "transferFrom(address,address,uint256)": {"details": "Calling Conditions: - {CoboERC20} is not paused. - The `from` is allowed to send tokens. (checked internally by {_requireAccess}) - The `to` is allowed to receive tokens. (checked internally by {_requireAccess}) - `from` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_transfer}) - `to` is a non-zero address. (checked internally by {ERC20Upgradeable}.{_transfer}) - `amount` is not greater than `from`'s balance or caller's allowance of `from`'s funds. (checked internally   by {ERC20Upgradeable}.{transferFrom}) - `amount` is greater than 0. (checked internally by {_spendAllowance})", "params": {"amount": "The number of tokens that will be sent to the `to` address.", "from": "The address that tokens will be transferred on behalf of.", "to": "The address that will receive the tokens."}, "returns": {"_0": "True if the function was successful."}}, "unpause()": {"details": "Calling Conditions: - The caller must hold the \"MANAGER_ROLE\" role. This function emits an {Unpaused} event as part of {PausableUpgradeable._unpause}."}, "upgradeToAndCall(address,bytes)": {"custom:oz-upgrades-unsafe-allow-reachable": "delegatecall", "details": "Upgrade the implementation of the proxy to `newImplementation`, and subsequently execute the function call encoded in `data`. Calls {_authorizeUpgrade}. Emits an {Upgraded} event."}, "version()": {"details": "This function get the latest deployment version from the {Initializable}.{_getInitializedVersion}. With every new deployment, the version number will be incremented.", "returns": {"_0": "The version of the contract."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"BURNER_ROLE()": {"notice": "The Access Control identifier for the Burner Role. An account with \"BURNER_ROLE\" can burn tokens from their own address."}, "MANAGER_ROLE()": {"notice": "The Access Control identifier for the Manager Role. An account with \"MANAGER_ROLE\" can update the contract URI, unpause the contract, toggle the access list status, update the access list, and update the block list."}, "MINTER_ROLE()": {"notice": "The Access Control identifier for the Minter Role. An account with \"MINTER_ROLE\" can mint tokens to the specified address."}, "PAUSER_ROLE()": {"notice": "The Access Control identifier for the Pauser Role. An account with \"PAUSER_ROLE\" can pause the contract."}, "SALVAGER_ROLE()": {"notice": "The Access Control identifier for the Salvager Role. An account with \"SALVAGER_ROLE\" can salvage native and ERC20 tokens from the contract."}, "UPGRADER_ROLE()": {"notice": "The Access Control identifier for the Upgrader Role. An account with \"UPGRADER_ROLE\" can upgrade the implementation contract address."}, "accessListAdd(address[])": {"notice": "This function adds a list of given address to the AccessList. This will allow the specified addresses to interact with the CoboERC20 contract. The function can be called by the address which has the \"MANAGER_ROLE\". The access list is disabled by default."}, "accessListEnabled()": {"notice": "State"}, "accessListRemove(address[])": {"notice": "This function removes a list of given address from the AccessList. The function can be called by the address which has the \"MANAGER_ROLE\"."}, "blockListAdd(address[])": {"notice": "This function adds a list of given address to the BlockList. This will block the specified addresses from interacting with the CoboERC20 contract. The function can be called by the address which has the \"MANAGER_ROLE\"."}, "blockListRemove(address[])": {"notice": "This function removes a list of given address from the BlockList. The function can be called by the address which has the \"MANAGER_ROLE\"."}, "burn(uint256)": {"notice": "This is a function used to burn tokens. The caller will burn tokens from their own address."}, "burnFrom(address,uint256)": {"notice": "This is a function used to burn tokens from the specified address. The caller will burn tokens from the specified address."}, "contractUri()": {"notice": "This field is a URI (Uniform Resource Identifier) that points to a JSON file with metadata about the contract."}, "contractUriUpdate(string)": {"notice": "This is a function used to update `contractUri` field."}, "getAccessList()": {"notice": "This function returns the list of addresses that are in the access list."}, "getBlockList()": {"notice": "This function returns the list of addresses that are in the BlockList."}, "initialize(string,string,string,address)": {"notice": "This function configures the CoboERC20 contract with the initial state and granting privileged roles."}, "isAccessListed(address)": {"notice": "This function checks if an address is present in the AccessList. By doing so, it confirms that whether the  address is allowed to participate in the system."}, "isBlockListed(address)": {"notice": "This function checks if an address is present in the BlockList. By doing so, it confirms that whether the  address is blocked from participating in the system."}, "mint(address,uint256)": {"notice": "This is a function used to issue new tokens. The caller will issue tokens to the `to` address."}, "pause()": {"notice": "This is a function used to pause the contract."}, "renounceRole(bytes32,address)": {"notice": "This function renounces an Access Control role from an account, except for the \"DEFAULT_ADMIN_ROLE\"."}, "revokeRole(bytes32,address)": {"notice": "This function revokes an Access Control role from an account, except for the _msgSender()'s \"DEFAULT_ADMIN_ROLE\"."}, "salvageERC20(address,uint256)": {"notice": "A function used to salvage ERC20 tokens sent to the contract using this abstract contract."}, "salvageNative(uint256)": {"notice": "A function used to salvage native token sent to the contract using this abstract contract."}, "supportsInterface(bytes4)": {"notice": "This is a function used to check if an interface is supported by this contract."}, "toggleAccesslist(bool)": {"notice": "This function toggles the access list. The function can be called by the address which has the \"MANAGER_ROLE\". The access list is disabled by default."}, "transfer(address,uint256)": {"notice": "This is a function used to transfer tokens from the sender to the `to` address."}, "transferFrom(address,address,uint256)": {"notice": "This is a function used to transfer tokens on behalf of the `from` address to the `to` address. This function emits an {Approval} event as part of {ERC20Upgradeable._approve}. This function emits a {Transfer} event as part of {ERC20Upgradeable._transfer}."}, "unpause()": {"notice": "This is a function used to unpause the contract."}, "version()": {"notice": "This is a function used to get the version of the contract."}}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/contracts/", "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "ds-test/=lib/forge-std/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts-upgradeable/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts-upgradeable/lib/halmos-cheatcodes/src/", "openzeppelin-contracts-upgradeable/=lib/openzeppelin-contracts-upgradeable/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 20000}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/CoboERC20/CoboERC20.sol": "CoboERC20"}, "evmVersion": "london", "libraries": {}, "viaIR": true}, "sources": {"lib/openzeppelin-contracts-upgradeable/contracts/access/AccessControlUpgradeable.sol": {"keccak256": "0xca0f8798297e39106cad374524780916b7ea20033641df3a9102aae253099aeb", "urls": ["bzz-raw://77d5c809873db24e02fc288ad506087853911f5daf0fb27e499333b4e75e3af1", "dweb:/ipfs/QmWNqoEj7NH3neSNNfoHwXfynrGqJu5JsvMfHe8fZB7isR"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/access/extensions/AccessControlEnumerableUpgradeable.sol": {"keccak256": "0xce21b42f07ce8d05b7fa3ac8581d8c26f9458a0d80e9d1c75d73b96612e0ed9e", "urls": ["bzz-raw://133fedaa75f3f5d307f6872c47f6c12bdf9f5c8df01b1357fc283d2dccbfaa33", "dweb:/ipfs/QmbPS26D88gd5ahpedRC2YByajf4gWJ7EQvHoQwZeneadF"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol": {"keccak256": "0xdb4d24ee2c087c391d587cd17adfe5b3f9d93b3110b1388c2ab6c7c0ad1dcd05", "urls": ["bzz-raw://ab7b6d5b9e2b88176312967fe0f0e78f3d9a1422fa5e4b64e2440c35869b5d08", "dweb:/ipfs/QmXKYWWyzcLg1B2k7Sb1qkEXgLCYfXecR9wYW5obRzWP1Q"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/UUPSUpgradeable.sol": {"keccak256": "0x574a7451e42724f7de29e2855c392a8a5020acd695169466a18459467d719d63", "urls": ["bzz-raw://5bc189f63b639ee173dd7b6fecc39baf7113bf161776aea22b34c57fdd1872ec", "dweb:/ipfs/QmZAf2VtjDLRULqjJkde6LNsxAg12tUqpPqgUQQZbAjgtZ"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/token/ERC20/ERC20Upgradeable.sol": {"keccak256": "0x776f40d448eba02791172036fcc6dfbe42fd4dec32b18ba49879d6ed474271ad", "urls": ["bzz-raw://286b8b7de752a268ce097ce87a0979d1f9c37d052cff0a4b9bd59fd0c34232ec", "dweb:/ipfs/QmUti8SGn5TFBNSPAwB2EBmUW6CYCU6sUKgK9iFdFxhjMJ"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol": {"keccak256": "0xdbef5f0c787055227243a7318ef74c8a5a1108ca3a07f2b3a00ef67769e1e397", "urls": ["bzz-raw://08e39f23d5b4692f9a40803e53a8156b72b4c1f9902a88cd65ba964db103dab9", "dweb:/ipfs/QmPKn6EYDgpga7KtpkA8wV2yJCYGMtc9K4LkJfhKX2RVSV"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/MulticallUpgradeable.sol": {"keccak256": "0xe5775eb1fb17165cd191e8f8b2232dbea8765e7e610eaa3d6e52feead793ec5a", "urls": ["bzz-raw://352614aea75c3d913cbcabb528be3d6c3335c3c77da41d59486a3193069dd095", "dweb:/ipfs/QmR3Nabxfme6tHrAMJCyK4MWZtpund2c4R7aFKmea3sGZM"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/PausableUpgradeable.sol": {"keccak256": "0xa6bf6b7efe0e6625a9dcd30c5ddf52c4c24fe8372f37c7de9dbf5034746768d5", "urls": ["bzz-raw://8c353ee3705bbf6fadb84c0fb10ef1b736e8ca3ca1867814349d1487ed207beb", "dweb:/ipfs/QmcugaPssrzGGE8q4YZKm2ZhnD3kCijjcgdWWg76nWt3FY"], "license": "MIT"}, "lib/openzeppelin-contracts-upgradeable/contracts/utils/introspection/ERC165Upgradeable.sol": {"keccak256": "0x2f0ea26703f46fc430ad9202a63f07521d234b9a94a1a7b019f4973bed7a35a0", "urls": ["bzz-raw://ccf228512ceb777b145d339b50b8b6a72393140cdd61bcb7bf842a89230e4fe7", "dweb:/ipfs/QmeWaUw193GDphNkPhJnpHQpfJBrv4v2QRbfGTdfYNTiEE"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/extensions/IAccessControlEnumerable.sol": {"keccak256": "0xca774fbe0568762efdc1a7cba31f09549c7fa96dbe97410f4843fa2f0bc000a3", "urls": ["bzz-raw://0187ffdbf3d61b6d86cba4fcd9826e53d876987d620533ee84c681bdaf0f3ba3", "dweb:/ipfs/QmVJDqdJv6uzHY7ifncfv2QJep8XTzS3bGb4s5Exhuv86m"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0x9b6b3e7803bc5f2f8cd7ad57db8ac1def61a9930a5a3107df4882e028a9605d7", "urls": ["bzz-raw://da62d6be1f5c6edf577f0cb45666a8aa9c2086a4bac87d95d65f02e2f4c36a4b", "dweb:/ipfs/QmNkpvBpoCMvX8JwAFNSc5XxJ2q5BXJpL5L1txb4QkqVFF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0xde7e9fd9aee8d4f40772f96bb3b58836cbc6dfc0227014a061947f8821ea9724", "urls": ["bzz-raw://11fea9f8bc98949ac6709f0c1699db7430d2948137aa94d5a9e95a91f61a710a", "dweb:/ipfs/QmQdfRXxQjwP6yn3DVo1GHPpriKNcFghSPi94Z1oKEFUNS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1967.sol": {"keccak256": "0xb25a4f11fa80c702bf5cd85adec90e6f6f507f32f4a8e6f5dbc31e8c10029486", "urls": ["bzz-raw://6917f8a323e7811f041aecd4d9fd6e92455a6fba38a797ac6f6e208c7912b79d", "dweb:/ipfs/QmShuYv55wYHGi4EFkDB8QfF7ZCHoKk2efyz3AWY1ExSq7"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xce41876e78d1badc0512229b4d14e4daf83bc1003d7f83978d18e0e56f965b9c", "urls": ["bzz-raw://a2608291cb038b388d80b79a06b6118a42f7894ff67b7da10ec0dbbf5b2973ba", "dweb:/ipfs/QmWohqcBLbcxmA4eGPhZDXe5RYMMEEpFq22nfkaUMvTfw1"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC1822.sol": {"keccak256": "0xc42facb5094f2f35f066a7155bda23545e39a3156faef3ddc00185544443ba7d", "urls": ["bzz-raw://d3b36282ab029b46bd082619a308a2ea11c309967b9425b7b7a6eb0b0c1c3196", "dweb:/ipfs/QmP2YVfDB2FoREax3vJu7QhDnyYRMw52WPrCD4vdT2kuDA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b", "urls": ["bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b", "dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol": {"keccak256": "0x8decfa54cec979c824b044b8128cd91d713f72c71fd7dfa54974624d8c949898", "urls": ["bzz-raw://271f914261a19d87117a777e0924ada545c16191ef9b00cc40b0134fc14ebc70", "dweb:/ipfs/QmdvVNWHGHQrGGPonZJs5NuzTevTjZRM2zayKrDJf7WBA2"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol": {"keccak256": "0xc59a78b07b44b2cf2e8ab4175fca91e8eca1eee2df7357b8d2a8833e5ea1f64c", "urls": ["bzz-raw://5aa4f07e65444784c29cd7bfcc2341b34381e4e5b5da9f0c5bd00d7f430e66fa", "dweb:/ipfs/QmWRMh4Q9DpaU9GvsiXmDdoNYMyyece9if7hnfLz7uqzWM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0x70f2f713b13b7ce4610bcd0ac9fec0f3cc43693b043abcb8dc40a42a726eb330", "urls": ["bzz-raw://c13d13304ac79a83ab1c30168967d19e2203342ebbd6a9bbce4db7550522dcbf", "dweb:/ipfs/QmeN5jKMN2vw5bhacr6tkg78afbTTZUeaacNHqjWt4Ew1r"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x982c5cb790ab941d1e04f807120a71709d4c313ba0bfc16006447ffbd27fbbd5", "urls": ["bzz-raw://8150ceb4ac947e8a442b2a9c017e01e880b2be2dd958f1fa9bc405f4c5a86508", "dweb:/ipfs/QmbcBmFX66AY6Kbhnd5gx7zpkgqnUafo43XnmayAM7zVdB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Address.sol": {"keccak256": "0xaaa1d17c1129b127a4a401db2fbd72960e2671474be3d08cae71ccdc42f7624c", "urls": ["bzz-raw://cb2f27cd3952aa667e198fba0d9b7bcec52fbb12c16f013c25fe6fb52b29cc0e", "dweb:/ipfs/QmeuohBFoeyDPZA9JNCTEDz3VBfBD4EABWuWXVhHAuEpKR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Arrays.sol": {"keccak256": "0x55a4fdb408e3db950b48f4a6131e538980be8c5f48ee59829d92d66477140cd6", "urls": ["bzz-raw://3e1ad251e692822ce1494135a4ecb5b97c19b90aa82418fd2959ce32017953fd", "dweb:/ipfs/QmT6N7mf6heZYhY2BAQ5kwZp9o3SXzGVdkMqUszx67WRDN"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Comparators.sol": {"keccak256": "0x302eecd8cf323b4690e3494a7d960b3cbce077032ab8ef655b323cdd136cec58", "urls": ["bzz-raw://49ba706f1bc476d68fe6c1fad75517acea4e9e275be0989b548e292eb3a3eacd", "dweb:/ipfs/QmeBpvcdGWzWMKTQESUCEhHgnEQYYATVwPxLMxa6vMT7jC"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Errors.sol": {"keccak256": "0x6afa713bfd42cf0f7656efa91201007ac465e42049d7de1d50753a373648c123", "urls": ["bzz-raw://ba1d02f4847670a1b83dec9f7d37f0b0418d6043447b69f3a29a5f9efc547fcf", "dweb:/ipfs/QmQ7iH2keLNUKgq2xSWcRmuBE5eZ3F5whYAkAGzCNNoEWB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/SlotDerivation.sol": {"keccak256": "0x67672e4ca1dafdcc661d4eba8475cfac631fa0933309258e3af7644b92e1fb26", "urls": ["bzz-raw://30192451f05ea5ddb0c18bd0f9003f098505836ba19c08a9c365adf829454da2", "dweb:/ipfs/QmfCuZSCTyCdFoSKn7MSaN6hZksnQn9ZhrZDAdRTCbwGu2"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0xecd5f3c702f549fb88555e44e5f2415a4dfd6db09081aec7e98c26b6a3739c06", "urls": ["bzz-raw://ed40e907a6d80458a0556a609d0d3991d718c20f9f1f21475e5ec739af523231", "dweb:/ipfs/QmejkwADsZRxkusNU94sswMJfpLrbq8RkJTbNccUEQpc7Y"], "license": "MIT"}, "src/CoboERC20/CoboERC20.sol": {"keccak256": "0x328d6a1550967347dbe6e7ca8c9bd7b80e73c5257c563842fb083d06495436ed", "urls": ["bzz-raw://ce35c5bde3dbb5e6a07c944ce8c54f58afaeefac93e5af54c1327d92374281a5", "dweb:/ipfs/QmRhnQsjWGKWys8GAStAE4Dqg6VYh2stjqbjPPRs75DWTY"], "license": "LGPL-3.0-only"}, "src/CoboERC20/library/Errors/LibErrors.sol": {"keccak256": "0x54a1b1b5b2e1d8ef12871c6d44f8087235022c0e41cfd0bf081f5802a560cbd6", "urls": ["bzz-raw://c4c0c9a4d3217ba26cf466a8245b54636f456de5f4d8c0c356998bd8ba2fc85b", "dweb:/ipfs/QmTvGC9oTkgGB9CMVvkpSHYZZv9sGmPmzRVznkueS1WCcu"], "license": "LGPL-3.0-only"}, "src/CoboERC20/library/Utils/AccessListUpgradeable.sol": {"keccak256": "0x2765e89ce9d2f7bbfea3b662a3e98e754c5360e04ff020158fd9b1faea926621", "urls": ["bzz-raw://b9be8dc8b48947360efa4d71c4a33e7e3cf57ed37db77547bf3285808627a96c", "dweb:/ipfs/Qmea8FS9HR1xj5mfJ4dCMuWq7wJgsGL19PnXcMjWwm84VA"], "license": "LGPL-3.0-only"}, "src/CoboERC20/library/Utils/ContractUriUpgradeable.sol": {"keccak256": "0xe57a2a3a611c23b1e95b81544bbdded20690c58f0acf91a084dc57c1de1ea850", "urls": ["bzz-raw://bb4803cf2fd067f99dca3ed028e45e26fc27e43c3bd118360694e7083564f252", "dweb:/ipfs/QmcVC2PhWji6nWNj7fAz61fgBuc8JZmCfj7NNeUwZ9ru5u"], "license": "LGPL-3.0-only"}, "src/CoboERC20/library/Utils/PauseUpgradeable.sol": {"keccak256": "0x6fa557a4d62792cf029371b95eb5842e56bd7d4ac773684a81a7069942752e20", "urls": ["bzz-raw://130678ddd6592952a59ef7c0ff8fc7cf3aef47bef91595340f6e8d091016656b", "dweb:/ipfs/QmWhoGHo4yMgtU1dK3mNEDHqZ474MWXEuT3sUTJxdBP1Rf"], "license": "LGPL-3.0-only"}, "src/CoboERC20/library/Utils/RoleAccessUpgradeable.sol": {"keccak256": "0xa04888487c9afc8c4a88ad2ef8be02e614b08c71d3c5876cba4e9231788a5f96", "urls": ["bzz-raw://dd21af034114e3d2d4d64a268c2091d33d15f4bd8927546db9daa715bdbf37b7", "dweb:/ipfs/QmbCVy1463saVfiydAmDszFKEVXJp9BY6QvD2FSScEKKm3"], "license": "LGPL-3.0-only"}, "src/CoboERC20/library/Utils/SalvageUpgradeable.sol": {"keccak256": "0x4af66e1af93a0df1c4a90fec0e6b1cc31209ac5d530030537784b930bb647192", "urls": ["bzz-raw://3d7f046c53c3acab26e0f2820b17da61ec008e53c8055d947d20bd6a24ac0fd5", "dweb:/ipfs/QmevZ8iweUsTRHrGpd4vnEBAooiiVssbTgwUKuzazAaiSX"], "license": "LGPL-3.0-only"}}, "version": 1}, "id": 57}