from django.conf import settings
from django.test import Client, TestCase


class CoboTestCase(TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.client = Client()

    def test_settings(self):
        self.assertTrue(hasattr(settings, "PROJECT_NAME"))

    def test_api(self):
        ret = self.client.get("/v1/test/ping")
        self.assertEqual(ret.status_code, 200)
        result = ret.json()
        self.assertEqual(result["success"], True)
        self.assertEqual(result["result"], "pong")
