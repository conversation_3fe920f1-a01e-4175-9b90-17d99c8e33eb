import os
from datetime import datetime

from celery.signals import setup_logging
from cobo_libs.utils.celery.app import PatchedCelery
from django.conf import settings

# set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "main.settings")


class CoboCelery(PatchedCelery):
    def now(self):
        """Return the current time and date as a datetime."""
        return datetime.now(self.timezone)


app = CoboCelery(settings.PROJECT_NAME.lower())
app.conf.broker_url = settings.REDIS_CELERY_DB_LOC
# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object("django.conf:settings", namespace="CELERY")


@setup_logging.connect
def config_loggers(*args, **kwags):
    from logging.config import dictConfig

    dictConfig(settings.LOGGING)


# Load task modules from all registered Django apps configs.
app.autodiscover_tasks()
