"""
URL configuration for portal_tokenization_app project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.urls import include, path
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)

from main.views.views import TestPingApi

urlpatterns = [
    path("v1/test/ping/", TestPingApi.as_view()),
    # Tokenization APIs
    path("", include("apps.tokenization.urls")),
]

if getattr(settings, "ENABLE_SWAGGER", False):
    urlpatterns += [
        # 其他 URL 配置
        path("internal/api/schema/", SpectacularAPIView.as_view(), name="schema"),
        path(
            "internal/api/schema/swagger-ui/",
            SpectacularSwaggerView.as_view(url_name="schema"),
            name="swagger-ui",
        ),
        path(
            "internal/api/schema/redoc/",
            SpectacularRedocView.as_view(url_name="schema"),
            name="redoc",
        ),
    ]

urlpatterns += [path("", include("app_libs.auth.urls"))]
urlpatterns += [path("", include("app_libs.mfa.urls"))]
urlpatterns += [path("", include("cobo_libs.aladdin.urls"))]
