# django rest framework
REST_FRAMEWORK = {
    "DEFAULT_THROTTLE_RATES": {
        "rate_limit_by_ip": "2000/m",  # 每分钟200次
    },
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "app_libs.auth.authentications.cookie.CookieAuthentication",
        # 其他认证类...
    ],
    "DEFAULT_GENERATOR_CLASS": "cobo_libs.api.decorator.drf.drf_generators.CoboSchemaGenerator",
}

COBO_SPECTACULAR_RESPONSE_WRAPPER = (
    "cobo_libs.api.decorator.drf.drf.dynamic_response_serializer"
)

SPECTACULAR_EXCLUDE_PATHS = [
    "/mpc_callback/v2/check/",
]
