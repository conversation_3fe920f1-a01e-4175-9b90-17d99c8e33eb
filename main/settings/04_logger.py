import os

import sentry_sdk
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

try:
    import git

    _REPO = git.Repo(search_parent_directories=True)
    GIT_SHA = _REPO.head.object.hexsha
except Exception:
    GIT_SHA = None

# Sentry
# https://docs.sentry.io/platforms/python/django/
SENTRY_DSN = locals().get("SENTRY_DSN", None)

if SENTRY_DSN is not None:
    # https://docs.sentry.io/error-reporting/configuration/filtering/?platform=python
    def filter_exceptions(event, hint):
        # Ignore errors from specific loggers.

        if event.get("logger", "") in ["django.security.DisallowedHost", "backoff"]:
            return None

        return event

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(),
            RedisIntegration(),
            CeleryIntegration(),
            SqlalchemyIntegration(),
        ],
        before_send=filter_exceptions,
        release=GIT_SHA,
    )

# Logstash
LOGSTASH_HOST = locals().get("LOGSTASH_HOST", "localhost")
LOGSTASH_PORT = locals().get("LOGSTASH_PORT", 5959)
LOGGING_COMMON_HANDLERS = ["console", "logstash"]
LOGGING_LOGSTASH_HANDLERS = ["logstash"]

ENV = locals().get("ENV", os.environ.get("DJANGO_ENV", "LOCAL_DEV"))

# Logging
# See http://docs.djangoproject.com/en/dev/topics/logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "root": {"level": "ERROR", "handlers": LOGGING_LOGSTASH_HANDLERS},
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(pathname)s#%(lineno)d:\n %(message)s"
        },
        "concise": {"format": "%(levelname)s %(asctime)s %(message)s"},
        "lean": {"format": "%(asctime)s: %(message)s"},
    },
    "filters": {
        "require_debug_false": {"()": "django.utils.log.RequireDebugFalse"},
        "add_cobo_tag": {"()": "cobo_libs.utils.logger.AddCoboTagFilter"},
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "lean",
        },
        "logstash": {
            "level": "DEBUG",
            "class": "cobo_libs.utils.logstash.handler.PatchedTCPLogstashHandler",
            "host": LOGSTASH_HOST,
            "port": LOGSTASH_PORT,
            "version": 1,
            "message_type": locals().get("PROJECT_NAME").lower() + "-" + ENV.lower(),
            "fqdn": False,
            "tags": ["django.request"],
            "filters": ["add_cobo_tag"],
        },
    },
    "loggers": {
        "django": {"handlers": LOGGING_COMMON_HANDLERS, "propagate": True},
        "django.request": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "django.security.DisallowedHost": {
            "handlers": LOGGING_LOGSTASH_HANDLERS,
            "propagate": False,
        },
        "django.controllers": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "django.celery": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "celery": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "cobo": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "cobo_libs": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "timing": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "apps": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "bridge": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "exchange": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
        "graphql": {
            "handlers": LOGGING_COMMON_HANDLERS,
            "level": "INFO",
            "propagate": False,
        },
    },
}
