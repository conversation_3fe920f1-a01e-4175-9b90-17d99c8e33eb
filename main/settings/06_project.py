# Portal API
PORTAL_DEV_API_HOST = ""
PORTAL_CALLBACK_PUB_KEY = ""
PORTAL_APP_ID = ""
PORTAL_TOKENIZATION_APP_ID = ""
PORTAL_APP_PRIVATE_KEY = ""
PORTAL_API_PRIVATE_KEY = ""

# Auth
AUTH_JWKS_URL = f"{PORTAL_DEV_API_HOST}/oauth/authorize/jwks.json"
AUTH_JWK_ISS = ""
AUTH_SESSION_EXPIRY = 1800  # seconds

# PORTAL_TOKEN_PRIVATE_KEY = (
#     "c0d4de7c2867ff1409311668921b22b213a83ed3382906b4f081475001426446"
# )
PORTAL_APP_PRIVATE_KEY = (
    "ec60493f183c21dd6d6d8dec1b0c3f61d0ae4b661af5a6894e7149e2afae9494"
)

CONFIG_SESSION_CODE = "user_session_code"

# 允许设置 cookie 的 HTTP 方法白名单
SET_COOKIE_METHOD_WHITELIST = ["GET", "POST"]

# org token refresh before token expiration time
ORG_TOKEN_AUTO_REFRESH_TIME = 60 * 60 * 2

WAAS_REQUEST_TIMEOUT = 10

WEB3_PROVIDER_MAP = {
    "BASE_ETH": "http://base_eth.rpc.cobo.one",
    "ETH": "http://geth.rpc.cobo.one",
    "MODE_ETH": "http://mode_eth.rpc.cobo.one",
    "MANTA_ETH": "http://manta_eth.rpc.cobo.one",
    "MNT": "http://mnt.rpc.cobo.one",
    "ARBITRUM_ETH": "http://arb.rpc.cobo.one",
    "AVAXC": "http://avaxc.rpc.cobo.one/ext/bc/C/rpc",
    "MATIC": "http://matic.rpc.cobo.one",
    "BSC_BNB": "http://bsc_bnb.rpc.cobo.one",
    "OPT_ETH": "http://opt2.rpc.cobo.one",
    "XDAI": "http://xdai.rpc.cobo.one",
    "SCROLL_ETH": "http://scroll_eth.rpc.cobo.one",
    "HOLESKY_ETH": "http://holesky_eth.rpc.cobo.one",
    "SETH": "http://teth_sepolia.rpc.cobo.one",
}
