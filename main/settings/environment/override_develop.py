PORTAL_APP_API_HOST = "https://api.dev.cobo.com"
PORTAL_DEV_API_HOST = "https://api.dev.cobo.com/v2"
PORTAL_WEB_API_HOST = "https://api.dev.cobo.com/web/v2"

# PORTAL_CALLBACK_PUB_KEY = (
#     "a04ea1d5fa8da71f1dcfccf972b9c4eba0a2d8aba1f6da26f49977b08a0d2718"
# )
# PORTAL_APP_ID = "cobo-staking"
PORTAL_TOKENIZATION_APP_ID = "Cr483FhRyLPv5IHE7Jrn5nokijtlg6Ox"
PORTAL_APP_ID = "Cr483FhRyLPv5IHE7Jrn5nokijtlg6Ox"
COINS_ORG_ID = "bde744c6-1486-45f2-aba4-0d0d3176f282"

AUTH_JWKS_URL = f"{PORTAL_DEV_API_HOST}/oauth/authorize/jwks.json"
AUTH_JWK_ISS = "https://portal.dev.cobo.com/"

CORS_ALLOWED_ORIGINS = ["https://tokenization.apps.dev.cobo.com"]
SESSION_COOKIE_SAMESITE = None

ALADDIN_SCRIPT_PUBKEY = (
    "029c81f5b374fdb309d69a80f0a96db5da4a918bdd3d9306c61bea0f9ab56bd527"
)

COBO_ENCRYPT_KEYS = {"5e177990": "hello,world!0000"}
COBO_ENCRYPT_DEFAULT_KEY_HASH = "5e177990"
COBO_ENCRYPT_HASH_FIELD_SALT = "localhost"

WEB3_PROVIDER_MAP = {
    "MODE_ETH": "http://mode_eth.rpc-prod.cobo.one",
    "MANTA_ETH": "http://manta_eth.rpc-prod.cobo.one",
    "MNT": "http://mnt.rpc-prod.cobo.one",
    "BASE_ETH": "http://base_eth.rpc-prod.cobo.one",
    "ARBITRUM_ETH": "http://arb.rpc-prod.cobo.one",
    "AVAXC": "http://avaxc.rpc-prod.cobo.one/ext/bc/C/rpc",
    "MATIC": "http://matic.rpc-prod.cobo.one",
    "BSC_BNB": "http://bsc_bnb.rpc-prod.cobo.one",
    "ETH": "http://geth_patch.rpc-prod.cobo.one",
    "OPT_ETH": "http://opt2.rpc-prod.cobo.one",
    "XDAI": "http://xdai.rpc-prod.cobo.one",
    "SCROLL_ETH": "http://scroll_eth.rpc-prod.cobo.one",
    "HOLESKY_ETH": "http://holesky_eth.rpc.cobo.one",
    "SETH": "http://teth_sepolia.rpc.cobo.one",
}
