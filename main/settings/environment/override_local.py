import ssl

import urllib3

PORTAL_APP_API_HOST = "https://api.sandbox.cobo.com"
PORTAL_DEV_API_HOST = "https://api.sandbox.cobo.com/v2"
PORTAL_WEB_API_HOST = "https://api.sandbox.cobo.com/web/v2"

# PORTAL_CALLBACK_PUB_KEY = (
#     "893d8a6112ae22429a7453599256391d7928e16870ecab888ee3ce65febada08"
# )
PORTAL_APP_ID = "t10asg4omvf72qILIlkvG8fXck2L9z8d"
COINS_ORG_ID = "ddb2847c-0917-47fe-bb88-c43f688cf5a9"

AUTH_JWKS_URL = f"{PORTAL_DEV_API_HOST}/oauth/authorize/jwks.json"
AUTH_JWK_ISS = "https://portal.sandbox.cobo.com/"

CORS_ALLOWED_ORIGINS = ["https://tokenization.apps.sandbox.cobo.com"]
SESSION_COOKIE_SAMESITE = None
AUTH_SESSION_EXPIRY = 604800  # seconds

COBO_ENCRYPT_KEYS = {"5e177990": "hello,world!0000"}
COBO_ENCRYPT_DEFAULT_KEY_HASH = "5e177990"
COBO_ENCRYPT_HASH_FIELD_SALT = "localhost"

REST_FRAMEWORK = {
    "DEFAULT_THROTTLE_RATES": {
        "rate_limit_by_ip": "2000/m",  # 每分钟200次
    },
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_AUTHENTICATION_CLASSES": [],
    "DEFAULT_GENERATOR_CLASS": "cobo_libs.api.decorator.drf.drf_generators.CoboSchemaGenerator",
}

# 禁用 SSL 验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置 SSL
ssl._create_default_https_context = ssl._create_unverified_context

WAAS_REQUEST_TIMEOUT = 15
