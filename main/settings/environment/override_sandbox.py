PORTAL_APP_API_HOST = "https://api.sandbox.cobo.com"
PORTAL_DEV_API_HOST = "https://api.sandbox.cobo.com/v2"
PORTAL_WEB_API_HOST = "https://api.sandbox.cobo.com/web/v2"

# PORTAL_CALLBACK_PUB_KEY = (
#     "893d8a6112ae22429a7453599256391d7928e16870ecab888ee3ce65febada08"
# )
PORTAL_TOKENIZATION_APP_ID = "t10asg4omvf72qILIlkvG8fXck2L9z8d"
PORTAL_APP_ID = "t10asg4omvf72qILIlkvG8fXck2L9z8d"
COINS_ORG_ID = "f926907c-8141-4cc2-9d12-7808590e2167"

AUTH_JWKS_URL = f"{PORTAL_DEV_API_HOST}/oauth/authorize/jwks.json"
AUTH_JWK_ISS = "https://portal.sandbox.cobo.com/"

CORS_ALLOWED_ORIGINS = ["https://tokenization.apps.sandbox.cobo.com"]
SESSION_COOKIE_SAMESITE = None
AUTH_SESSION_EXPIRY = 604800  # seconds

ENABLE_SWAGGER = True

COBO_ENCRYPT_KEYS = {"5e177990": "hello,world!0000"}
COBO_ENCRYPT_DEFAULT_KEY_HASH = "5e177990"
COBO_ENCRYPT_HASH_FIELD_SALT = "localhost"

COBO_MPC_CALLBACK = {
    "SERVICE_NAME": "cobo-mpc-callback-server",  # optional
    "TOKEN_EXPIRE_MINUTES": 5,  # optional default is 5
    "MPC_CALLBACK_CLIENT_PUBLIC_KEY": """-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA0M2Zq3WGqFYlq7fi3+mX
+8bHDT14I97GrdXwLeeOWKpTCEJ0wpCEPbfIgbEc3q/wDtVa3akDtw2KwSf7wgGR
ImvfLbIck/gSMF3B2c9o0qtQEamI7tnUZ4ExOt1fU24saikteAOzCtj5FXhClECx
Fmg3qHsmzqr23R2dw+O4w7K0gpXNVYpcyD5PObyubEtHOykbRr/TX0HQiT8EottU
OtNUScAg4WTYnYPPyaT/7Xs3ieMb+ASWADTsdxKoNshaZciqHrGlXdo5/DtJfLNc
zPwvlcHklijox6EvQ1WlSdd/OUQkYei3sbABB+nKC9s8MXcCymT+DGBiwL0WF2fl
+nunP7QOnsMecwB0rinbMfVh6CF0gJOVReyRbQNoHk9En+yQAjkEyLMA8ApJofuj
dTr0t0g9h7SYkNBhNNVrMUpYqIATXLngkvgp0p5pIFBPOYmOF8vTdbvV7NsSqy7r
7Krv4nHVeJHgvPQ7QkyRd+73sCJnDEd+ZoFxa4/GR8JkriGYJuwsRxXBkYyfCtxc
9B8TlSv6kOqbb6nZD9wcn6TgvmpZ9Ncsnle/qp/uDpQm8KrjIf3NZfDohv7YXDvs
vgthRzi3Y8YDd7hYQXSjQnMQR9jrPZQsFGSR+UiFp4rfNK6Zf6cqGAAiZ6P2BvBp
Ja0WPF0EgKwbYrDTxkA2S7MCAwEAAQ==
-----END PUBLIC KEY-----""",
    "MPC_CALLBACK_SERVICE_PRIVATE_KEY": """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************""",
    "REQUEST_VERIFIER": "apps.swap.tsscallback.verify.SwapVerifier",
}

ALADDIN_SCRIPT_PUBKEY = (
    "0213cac10d6350e8da0ad0ae077f80acec845a89948c1457d44c1297a62157035e"
)

WAAS_REQUEST_TIMEOUT = 15
