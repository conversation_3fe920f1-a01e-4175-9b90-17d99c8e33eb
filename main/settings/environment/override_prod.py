PORTAL_APP_API_HOST = "https://api.cobo.com"
PORTAL_DEV_API_HOST = "https://api.cobo.com/v2"
PORTAL_WEB_API_HOST = "https://api.cobo.com/web/v2"

# PORTAL_CALLBACK_PUB_KEY = (
#     "8d4a482641adb2a34b726f05827dba9a9653e5857469b8749052bf4458a86729"
# )
PORTAL_TOKENIZATION_APP_ID = "pF4GAasCmq68qUIH4Y0y17JKHMxcGpRP"
PORTAL_APP_ID = "pF4GAasCmq68qUIH4Y0y17JKHMxcGpRP"
COINS_ORG_ID = "3a7d0e63-39f1-4989-9704-79a976421b9d"

AUTH_JWKS_URL = f"{PORTAL_DEV_API_HOST}/oauth/authorize/jwks.json"
AUTH_JWK_ISS = "https://portal.cobo.com/"


CORS_ALLOWED_ORIGINS = ["https://tokenization.apps.cobo.com"]
SESSION_COOKIE_SAMESITE = None

ASSET_WHITE_ORG_LIST = {}

ALADDIN_SCRIPT_PUBKEY = (
    "023a04034317c343d1ee53a9f16bac8a099bcc38689ae140af55ba453fb1594d7f"
)

WEB3_PROVIDER_MAP = {
    "MODE_ETH": "http://mode_eth.rpc-prod.cobo.one",
    "MANTA_ETH": "http://manta_eth.rpc-prod.cobo.one",
    "MNT": "http://mnt.rpc-prod.cobo.one",
    "BASE_ETH": "http://base_eth.rpc-prod.cobo.one",
    "ARBITRUM_ETH": "http://arb.rpc-prod.cobo.one",
    "AVAXC": "http://avaxc.rpc-prod.cobo.one/ext/bc/C/rpc",
    "MATIC": "http://matic.rpc-prod.cobo.one",
    "BSC_BNB": "http://bsc_bnb.rpc-prod.cobo.one",
    "ETH": "http://geth_patch.rpc-prod.cobo.one",
    "OPT_ETH": "http://opt2.rpc-prod.cobo.one",
    "XDAI": "http://xdai.rpc-prod.cobo.one",
    "SCROLL_ETH": "http://scroll_eth.rpc-prod.cobo.one",
}
