import os

from split_settings.tools import include, optional


def is_ci_test():
    _is_ci_test = os.getenv("BUILD_TYPE", None) == "test"
    if not _is_ci_test:
        import sys

        cmd = sys.argv[0]
        _is_ci_test = (
            "pytest" in cmd or "py.test" == cmd or cmd.rfind("/py/test.py") != -1
        )  # pytest or py.test or python -m py.test
    return _is_ci_test is True


_main_settings = [
    optional("../../pre_settings.py"),
    "01_base.py",
    "02_common.py",
    "03_database.py",
    "04_logger.py",
    "05_celery.py",
    "06_project.py",
    "90_cobo_libs.py",
]
include(*_main_settings)


ENV = locals().get("ENV")
assert ENV, "ENV Not Found"
_ENV_PREFIX = ENV.split("_")[0]
assert _ENV_PREFIX, f"Illegal ENV. ENV: {ENV}"
_override_settings = [
    f"environment/override_{_ENV_PREFIX}.py".lower(),
    optional("../../tests/testenv/settings.py") if is_ci_test() else None,
    optional("../../local_settings.py"),
    "99_dynamic.py",
    optional("../../post_settings.py"),
]
_override_settings = list(i for i in _override_settings if i is not None)
include(*_override_settings)
