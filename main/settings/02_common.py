from corsheaders.defaults import default_headers

ALLOWED_HOSTS = [".cobo.com", "localhost"]

ROOT_URLCONF = f"{locals().get('PROJECT_DIR_NAME')}.urls"

# Application definition
INSTALLED_APPS = (
    [
        "django.contrib.auth",
        "django.contrib.contenttypes",
        "django.contrib.sessions",
    ]  # django official apps
    + ["corsheaders"]  # django extensions
    + [
        "apps.tokenization",
    ]  # project apps
    + [
        "cobo_libs.api",
        "cobo_libs.notification",
        "cobo_libs.cobo_mpc_callback",
        "cobo_libs.aladdin",
    ]  # cobo_libs apps
    + [
        "rest_framework",
        "drf_spectacular",  # 文档
        # "django_extensions",
    ]
    + [
        "app_libs.auth",
        "app_libs.mfa",
        "app_libs.coins",
    ]
)

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "app_libs.auth.middlewares.cookie.SetCookieMiddleware",
]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ]
        },
    }
]

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

SPECTACULAR_SETTINGS = {
    "TITLE": "tokenization app api spec",
    "DESCRIPTION": "tokenization app api spec",
    "VERSION": "v1.0",
    "SERVE_INCLUDE_SCHEMA": False,
    # OTHER SETTINGS
    "PREPROCESSING_HOOKS": [
        "cobo_libs.api.decorator.drf.drf_hooks.preprocessing_filter_spec"
    ],
}

WSGI_APPLICATION = f"{locals().get('PROJECT_DIR_NAME')}.wsgi.application"
ASGI_APPLICATION = f"{locals().get('PROJECT_DIR_NAME')}.asgi.application"

# CORS related
CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = False
CORS_ALLOWED_ORIGINS = []
CORS_ALLOW_HEADERS = (
    *default_headers,
    "accept-encoding",
    "BIZ-ORG-ID",
)

# django 3.2
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Internationalization
# https://docs.djangoproject.com/en/1.11/topics/i18n/
LANGUAGE_CODE = "en-us"
TIME_ZONE = "Asia/Shanghai"
USE_I18N = True
USE_TZ = True

INSTALLED_ALLOWED_HOSTS = [".main.com", "localhost"]

SCAN_URL = {
    "ETH": "https://etherscan.io/",
    "SETH": "https://sepolia.etherscan.io/",
    "BSC": "https://bscscan.com/",
}

SCAN_ICON_URL = {
    "ETH": "https://d.cobo.com/public/logos/Etherscan.png",
    "SETH": "https://d.cobo.com/public/logos/Etherscan.png",
    "BSC": "https://d.cobo.com/public/logos/Bscscan.png",
}
