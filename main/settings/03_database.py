import os

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": os.path.join(locals().get("BASE_DIR", ""), "db.sqlite3"),
    },
    "readonly": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": os.path.join(locals().get("BASE_DIR", ""), "db.sqlite3"),
    },
}
USE_SQL_POOL = True

# Redis
REDIS_HOST = locals().get("REDIS_HOST", "127.0.0.1")
REDIS_PORT = 6379
REDIS_URI = locals().get("REDIS_URI", f"redis://{REDIS_HOST}:{REDIS_PORT}")

REDIS_GENERAL_DB = locals().get("REDIS_GENERAL_DB", 0)
REDIS_GENERAL_DB_LOC = f"{REDIS_URI}/{REDIS_GENERAL_DB}"
REDIS_CELERY_DB = locals().get("REDIS_CELERY_DB", 9)
REDIS_CELERY_DB_LOC = f"{REDIS_URI}/{REDIS_CELERY_DB}"

# Cache
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_GENERAL_DB_LOC,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SOCKET_TIMEOUT": 30,
            "SOCKET_CONNECT_TIMEOUT": 5,
        },
    },
    "local": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "local",
        "TIMEOUT": 3600 * 24 * 8,
        "MAX_ENTRIES": 1000,
    },
}
