import os

PROJECT_NAME = locals().get("PROJECT_NAME", "Portal-Tokenization")
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = locals().get("DEBUG", False)

# Current env: PROD_AAA, SANDBOX_BBB or LOCAL_CCC
ENV = locals().get("ENV", os.environ.get("DJANGO_ENV", "LOCAL_DEV"))
IS_PROD_ENV = ENV.startswith("PROD")

# Paths
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PROJECT_DIR_NAME = os.path.basename(PROJECT_DIR)
BASE_DIR = os.path.dirname(PROJECT_DIR)
LOCALE_PATHS = [os.path.join(PROJECT_DIR, "locale")]
LOG_DIR = os.path.join(BASE_DIR, "logs")

try:
    os.makedirs(LOG_DIR, exist_ok=True)
except OSError:
    pass
