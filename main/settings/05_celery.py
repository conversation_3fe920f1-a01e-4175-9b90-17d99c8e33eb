import os
from typing import Dict

# Celery
CELERY_BROKER_URL = locals().get("REDIS_CELERY_DB_LOC")
CELERY_ENABLE_UTC = False
CELERY_TIMEZONE = locals().get("TIME_ZONE")
CELERY_PID_FILE = os.path.join(locals().get("LOG_DIR", ""), "celery_%n.pid")
CELERYD_MAX_TASKS_PER_CHILD = 100  # avoid memory leaks

# Celery auto-retry settings
# https://docs.celeryproject.org/en/latest/userguide/tasks.html#retrying
CELERY_MAX_RETRIES = 0  # Disable auto-retry for default
CELERY_RETRY_BACKOFF = 30
CELERY_AUTORETRY_FOR = (Exception,)
CELERY_RETRY_JITTER = True

# Celery beat schedule
CELERY_BEAT_SCHEDULE: Dict[str, dict] = {}

# Celery queue info
CELERY_DEFAULT_QUEUE = "default"
CELERY_ACCOUNT_QUEUE = "account"
# stuck celery queue max length
CELERY_QUEUE_DEFAULT_LENGTH = 100
CELERY_QUEUE_MAX_LENGTH = {
    CELERY_DEFAULT_QUEUE: 100,
    CELERY_ACCOUNT_QUEUE: 100,
}

# Celery task routes
CELERY_TASK_ROUTES = {
    "*": {"queue": CELERY_DEFAULT_QUEUE},
}
