[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.uv]
index-url = "https://pypi.org/simple"
extra-index-url = ["https://codeartifact.1cobo.com/pypi/default/simple/"]

[tool.uv.sources]
python-axolotl-curve25519 = { git = "https://github.com/hannob/python-axolotl-curve25519.git", rev = "901f4fb12e1290b72fbd26ea1f40755b079fa241" }

[project]
name = "portal-tokenization-app"
version = "0.1.0"
description = "Django-based tokenization management system"
readme = "README.md"
requires-python = ">=3.10,<3.11"
license = { text = "Proprietary" }
authors = [
    { name = "Cobo Team", email = "<EMAIL>" },
]
keywords = ["django", "tokenization", "crypto", "wallet"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Framework :: Django :: 4.2",
    "Intended Audience :: Developers",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
]

dependencies = [
    "app-libs==0.0.66",
    "asgiref==3.6.0",
    "channels==3.0.5",
    "cobo-app-python-api==0.1.62",
    "cobo-waas2-python-api==0.6.136",
    "cobo-libs==0.20.257", # main private repo
    "django-cors-headers==4.3.1",
    "django-split-settings==1.3.1",
    "django==4.2.13",
    "ipython==8.30.0",
    "python-axolotl-curve25519", # fix python-axolotl-curve25519 build failed issue, refs https://github.com/tgalal/python-axolotl-curve25519/pull/26
    "sentry-sdk[celery,django,sqlalchemy]==2.1.1",
    "web3==6.14.0",
    "ed25519>=1.5",
    "eth-typing==4.4.0",
    "safe-eth-py==5.8.0",
    "anchorpy==0.21.0",
]

[project.optional-dependencies]
dev = [
    "certifi==2025.1.31",
    # flower
    "bandit==1.6.2",
    "black==22.3.0",
    "coverage",
    "django_upgrade==1.21.0",
    "flake8==3.8.3",
    "ipython",
    "isort==5.1.4",
    "ossaudit==0.5.0",
    "pre-commit==2.19.0",
    "pysocks==1.7.1",
]
test = [
    "allure-pytest==2.13.2",
    "coverage",
    "mock",
    "pytest-cache==1.0",
    "pytest-cov==4.1.0",
    "pytest-django==4.7.0",
    "pytest-env==1.1.1",
    "pytest-faulthandler==2.0.1",
    "pytest-mock==3.12.0",
    "pytest-pep8==1.0.6",
    "pytest-timeout==2.2.0",
    "pytest-xdist==3.6.1",
    "pytest==7.4.3",
]

[project.scripts]
manage = "manage:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]
exclude = ["venv", "*.egg-info", "__pycache__", "*.pyc", "*.pyo", "build", "dist"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*", "venv/*"]

[tool.coverage.run]
source = ["."]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
    "manage.py",
    "*/settings/*",
    "*/wsgi.py",
    "*/asgi.py",
]

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false

[tool.bandit]
exclude_dirs = ["tests", "venv", "migrations"]
skips = ["B101"]  # assert_used

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "settings.test"
python_files = ["test_*.py", "*_test.py", "tests.py"]
addopts = [
    "--strict-markers",
    "--tb=short",
    "--reuse-db",
    "-v",
]
testpaths = ["tests"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    "migrations",
    "venv",
    ".venv",
]
