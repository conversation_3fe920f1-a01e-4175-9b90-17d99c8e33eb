{"celery-beat": {"template": "program", "settings": {"command": "celery -A main.celery --no-color beat --pidfile=/tmp/celerybeat.pid --schedule=/tmp/celerybeat-schedule"}, "tag": "celery_01"}, "celery-default": {"template": "program", "settings": {"command": "celery -A main.celery --no-color worker -Q default --concurrency=3 -n default"}, "tag": "celery_01"}, "channel": {"template": "fcgi-program", "settings": {"command": "daphne -u /tmp/daphne%(process_num)d.sock --fd 0 --access-log - main.asgi:application"}, "tag": "channel"}, "refresh-token": {"template": "program", "settings": {"command": "python3 manage.py auth_refresh_token"}, "tag": "service_01"}, "app_mfa_guard_service": {"template": "program", "settings": {"command": "python3 manage.py mfa_guard -i 1"}, "tag": "service_01"}}