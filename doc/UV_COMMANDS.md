# UV Package Manager Commands for Cobo Custody

This document provides a comprehensive guide for using UV package manager in the Cobo Custody project.

## Quick Start - Initialize Virtual Environment

### 1. Create and Initialize Virtual Environment from Existing Project

```bash
# Create a new virtual environment (UV will create .venv directory)
uv venv

source .venv/bin/activate

# Install all dependencies from pyproject.toml
uv sync --all-extras

# Verify installation
uv pip list

python manage.py migrate
```

### 2. Activate Virtual Environment

UV automatically manages the virtual environment, but you can also manually activate it:

```bash
# On macOS/Linux
source .venv/bin/activate

# On Windows
.venv\Scripts\activate

# Or use UV to run commands without activation
uv run python manage.py runserver
```

## Syncing requirements.txt Updates to UV

When `requirements.txt` is updated with new packages or version changes, follow these steps to sync changes to UV
configuration:

### Recommended Method: Using uv add with requirements file

```bash
# Sync all dependencies from requirements.txt with private index
uv add -r requirements.txt --index-url https://codeartifact.1cobo.com/pypi/default/simple

# After syncing, regenerate lock file
uv lock

# Install the updated dependencies
uv sync --all-extras
```

### Alternative Methods

#### Manual Update for Specific Packages

```bash
# Update a single package
uv add package-name==version --index-url https://codeartifact.1cobo.com/pypi/default/simple

# Example: Update bc-waas2
uv add bc-waas2==0.0.47 --index-url https://codeartifact.1cobo.com/pypi/default/simple
```

### Important Notes

- Always run `uv lock` after updating dependencies
- The `pyproject.toml` is the source of truth for UV
- Comments in `requirements.txt` are not preserved
- Git dependencies require special syntax in `pyproject.toml`
- Private packages require the `--index-url` parameter

## UV Command Reference

### Package Management Commands

```bash
# Add packages
uv add package-name                    # Add latest version
uv add package-name==1.2.3            # Add specific version
uv add package-name>=1.2.3            # Add with version constraint
uv add --dev package-name             # Add as dev dependency
uv add --optional test package-name   # Add to optional group
uv add -r requirements.txt            # Add from requirements file

# Remove packages
uv remove package-name                # Remove a package
uv remove --dev package-name          # Remove dev dependency

# Update packages
uv lock --upgrade                     # Update all packages
uv lock --upgrade-package pkg-name    # Update specific package
```

### Environment Commands

```bash
# Virtual environment
uv venv                              # Create new virtual environment
uv venv --python 3.10               # Create with specific Python version
uv venv venv-name                   # Create with custom name

# Sync dependencies
uv sync                             # Install dependencies from lock file
uv sync --all-extras               # Install all optional dependencies
uv sync --no-dev                   # Install only production dependencies
uv sync --reinstall                # Force reinstall all packages
uv sync --refresh                  # Refresh and sync

# Run commands
uv run python script.py            # Run Python script
uv run pytest                      # Run tests
uv run python manage.py runserver  # Run Django server
uv run which python               # Check Python path
```

### Inspection Commands

```bash
# List packages
uv pip list                        # List installed packages
uv pip show package-name          # Show package details
uv tree                           # Show dependency tree
uv tree --depth 2                 # Limit tree depth

# Search packages
uv search package-name            # Search for packages

# Check for issues
uv audit                          # Check for security vulnerabilities
uv check                          # Verify project configuration
```

### Lock File Commands

```bash
# Lock file operations
uv lock                           # Generate/update lock file
uv lock --refresh                # Refresh metadata and lock
uv lock --offline               # Use cached packages only
uv lock --upgrade               # Upgrade all dependencies
uv lock --upgrade-package pkg   # Upgrade specific package
```

### Export Commands

```bash
# Export dependencies
uv export --format requirements                    # Export to requirements.txt
uv export --format requirements --hashes          # With hashes
uv export --format requirements --no-dev          # Production only
uv export --format requirements > requirements.txt # Save to file
```

### Cache Management

```bash
# Cache operations
uv cache clean                    # Clear package cache
uv cache prune                   # Remove unused cache entries
uv cache dir                     # Show cache directory location
```

### Configuration Commands

```bash
# Show configuration
uv config show                   # Display current configuration
uv version                      # Show UV version

# Index URLs (for private packages)
uv add pkg --index-url URL      # Use custom index
uv add pkg --extra-index-url URL # Add additional index
```

### Development Workflow Commands

```bash
# Common development tasks
uv run python manage.py migrate          # Run migrations
uv run python manage.py test            # Run tests
uv run pytest -v                        # Run pytest
uv run black .                          # Format code
uv run flake8                          # Lint code
uv run mypy .                          # Type checking

# Interactive shell
uv run python                          # Python REPL
uv run ipython                        # IPython shell
uv run python manage.py shell         # Django shell
```

## Migration from pip

To migrate from existing pip/venv setup:

1. Ensure `pyproject.toml` is present with all dependencies
2. Run `uv lock` to generate the lock file
3. Run `uv sync` to install dependencies
4. Use `uv run` prefix for Python commands or activate the UV-managed virtual environment

## Notes

- UV automatically manages virtual environments, no need to manually create or activate them
- The lock file (`uv.lock`) should be committed to version control for reproducible builds
- UV is significantly faster than pip for dependency resolution and installation
- All pip commands can be replaced with `uv pip` equivalents if needed
- For private packages, always include `--index-url` when adding dependencies
