COBO project source code and how to set up the dev/prod environment

Contents
====

dev environment
-----

How to install python3 and venv on ubuntu 16.04 server

  https://www.digitalocean.com/community/tutorials/how-to-install-python-3-and-set-up-a-programming-environment-on-an-ubuntu-16-04-server

upgrade python3.5 to python3.6, required by 'secrets' module. We *SHOULD* install python3.6 on production environment for better security.

  http://ubuntuhandbook.org/index.php/2017/07/install-python-3-6-1-in-ubuntu-16-04-lts/









=================
mac local dev env setup normal problems:

0. python version error. 
  only python 3.6.x, please don't use python 3.7

1. can not find openssl relate head files.
  edit your .bash_profile, add the following:
  export LDFLAGS="-L/usr/local/opt/openssl/lib"
  export CPPFLAGS="-I/usr/local/opt/openssl/include"

2. database mysql relate errors:
  please using mysql@5.7， mysql 8.0 will fail the building ...
     
3. log relate error:
  build a log dir in src root
  ...

4. byte_string error: 
  pip uninstall pycrypto
  pip install pycryptodome==3.8.1

5.  you need to setup host file,  add r630， r730
  sudo vim /etc/hosts
  add the following:
  *************** r630
  *************** r730

6. need install fluxdb
  brew install influxdb. 
  build a table : telegraf table

7. mysql start data:
  you can run ./manager.py migrate to init mysql table 
  your may need some init data , you can go to r630 to dump some mysql data

Run unit tests

----

IMPORTANT: *arc unit*, and script inside testenv directory will use pytest as the unit test engine. Be sure to append new module path to the "testpaths" inside *pytest.ini* configuration in the project root.

Three approach to run test.

- *arc unit --engine=PytestTestEngine*
 - if you run *arc unit* on a linux box, make sure *php-xml* package is installed.
     on Debian/Ubuntu: sudo apt install php-xml
- *pytest [module | module/test_file.py]
- test script inside *testenv*
 - *[RUN_ENV=ci] tests/testenv/run_unittest.sh [-r]*
  - *-r* tox environment will be recreated
  - *RUN_ENV=ci* environment variable will let the unit test use mysql as backend db, otherwise sqlite will be used
- django test framework
 - *./manage.py test [module | module/tests.py]* if no module or test case specified, all test cases in the working directory will run
     if a test db exists, as db_ut.sqlite3 in local, django will prompt if test db can be destroied. Choose 'n', tests will stop.
refer `tests/testenv/README.md`
