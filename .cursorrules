# Cursor Rules for Portal Tokenization App

## ERC-20 API Development Rules

### 1. API装饰器规则
- 所有API视图必须使用 `cobo_extend_schema` 而不是 `extend_schema`
- 必须包含适当的 tags 和 description
- 需要认证的API使用 `AuthAPIView`，不需要认证的使用 `BaseAPIView` 并设置 `authentication_classes = []`

### 2. 序列化器规则
- 所有序列化器必须继承 `BaseModel` (from pydantic)
- 使用 `Field(..., description="...", example="...")` 定义字段
- 可选字段使用 `Optional[Type]` 和 `Field(None, ...)`
- 请求和响应数据必须有对应的序列化器定义
- 序列化器存放在 `apps/[app_name]/views/serializers/` 目录下
- 序列化器文件命名与对应views文件保持一致，如 `erc20_views.py` 对应 `erc20_serializers.py`

### 3. Swagger配置规则
- API标签定义在 `apps/[app_name]/views/swagger/view_swagger.py` 的 `SwaggerTags` 类中
- API描述定义在同文件的 `SwaggerDescription` 类中
- 标签命名格式：`[功能模块]_[具体功能]`，例如 `ERC20_TOKEN`

### 4. 响应格式规则
- 所有API响应必须遵循统一格式：
  ```json
  {
    "success": boolean,
    "result": object | null,
    "error": string | null
  }
  ```

### 5. 错误处理规则
- 使用 `logger.error()` 记录错误信息
- 返回用户友好的错误消息
- 不暴露内部技术细节

### 6. URL路由规则
- API版本前缀：`/v1/`
- ERC-20相关API：`/v1/erc20/`
- RESTful风格：
  - 创建：`POST /v1/erc20/token/create/`
  - 查询：`GET /v1/erc20/token/info/`
  - 转账：`POST /v1/erc20/transfer/`

### 7. 认证规则
- 写操作（创建、转账）需要认证
- 读操作（查询信息、余额、历史）可以不需要认证
- 使用 `authentication_classes = []` 明确表示不需要认证

### 8. 参数验证规则
- 必需参数在视图函数开始时进行验证
- 使用 `request.data` 获取POST数据
- 使用 `request.query_params.get()` 获取GET参数

### 9. 网络支持规则
- 支持的EVM网络：ethereum, bsc, polygon, arbitrum
- 网络参数使用 `ChoiceField` 限制可选值

### 10. 模拟数据规则
- 开发阶段使用mock数据，所有TODO注释需要在实际集成时替换
- mock数据要符合真实API的数据结构
- 包含合理的示例值（地址、哈希等） 